﻿<?xml version="1.0" encoding="utf-8"?>
<root Version="v2025.8.28">
  <Common ShowPackageView="True" ShowEditorAlias="False" UniqueBundleName="True" />
  <Package PackageName="DefaultPackage" PackageDesc="" AutoAddressable="True" SupportExtensionless="True" LocationToLower="False" IncludeAssetGUID="False" IgnoreRuleName="NormalIgnoreRule">
    <Group GroupActiveRule="EnableGroup" GroupName="Actor" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Actor" CollectGUID="9c67ce0c9edfd4e4aa1006ee39846d66" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Audios" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Audios/Battle" CollectGUID="498ee938435c948db970d84d8bc5b7b6" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Audios/HeartGoldOST" CollectGUID="0b1520d2f7fc6416cb177621c082fe4e" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Audios/PlatinumOST" CollectGUID="5b0dc1a67c33c43c18522c1683058453" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Audios/Fx" CollectGUID="bbe48b508002f42bb87f2c7055fefc73" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Audios/PokeCries" CollectGUID="135201b83590446ecb3a44393bbb01d8" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Configs" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Configs" CollectGUID="dd2928019aef34248af368b99bc53bea" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="DLL" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/DLL" CollectGUID="3aad79ec1ea08c24c891bd3c669d4125" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Effects" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Effects" CollectGUID="0fe175e1e1bd49a4ca71e66b6a9b7237" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Texture/Effect" CollectGUID="edae5ee44c518446eb009da7c8ca04bf" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Fonts" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Fonts" CollectGUID="2473375c9ee163a4b861278b38091455" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Materials" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Materials" CollectGUID="228b1547e7065d546ad0bf215fd6a276" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Scenes" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Scenes" CollectGUID="cace6ee6539f661419b5e5f8ae1c0146" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="UI" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/UI" CollectGUID="27e87d83814156648b58f380b834e046" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="UIRaw" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/UIRaw/Atlas" CollectGUID="6d11fa91acc253840a648b58f23db139" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/UIRaw/Raw" CollectGUID="6bc134b912ac6bb4399ea1bec4c11636" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Poke" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Texture/PokeTexture" CollectGUID="6493639a9fb584215b76e3b899712218" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/PokeData/json" CollectGUID="36c467c451add49b28ad209ecb188977" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Trainer" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Texture/Trainer" CollectGUID="f4a64ebe5d92748528861730a41da3ae" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Texture/Npc" CollectGUID="85c88b729e1084f378c0c54ae9ac0498" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Item" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Texture/Items" CollectGUID="2c325d65f0b884e858b12393171036fb" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Texture/Pokeballs" CollectGUID="9fc7685294c104fc38313a58e760e032" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Cozy" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/Plugins/CozyWeather" CollectGUID="07a054e6e85494ea3a03583e66c8b5e5" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Prefabs" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Prefabs/Weather" CollectGUID="5cbd3e56e5a984c50b080a5921f1c550" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Prefabs/NinTransitionsPlus.prefab" CollectGUID="38a5ff874fd0f4eb3b0b01ab0ff5b321" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Pokemon_DB" GroupDesc="" AssetTags="Pokemon_DB">
      <Collector CollectPath="Assets/AssetRaw/PokeData/DBRaw" CollectGUID="2dadfa8dcb2484730a6a05a71d6ba938" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackRawFile" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Map" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Map/Json" CollectGUID="36dda1c468374439ebc7df8d88cdb2e7" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Map/Controllers" CollectGUID="9ec80725f11334bb98edd11812e52a8b" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Map/Prefabs/EventObjects" CollectGUID="ddd2a5329f3c8438d86cff6e51d9341e" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="HeartGold" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/MapBakeResults/HeartGold" CollectGUID="e2fca99c642eb443687aed326e896697" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Map/AnimatorPrefabs/HeartGold" CollectGUID="5f28663bea4ba4aafb4711919f88cb55" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Platinum" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/MapBakeResults/Platinum" CollectGUID="313347355f4c74c53b18273b2d4db98c" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
      <Collector CollectPath="Assets/AssetRaw/Map/AnimatorPrefabs/Platinum" CollectGUID="9437fb0f692e34a3aa7cc8f8d0bb43c8" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="White2" GroupDesc="" AssetTags="" />
    <Group GroupActiveRule="EnableGroup" GroupName="Yarn" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/YarnSpinner" CollectGUID="b4aea6a88885a451d89a7771e5ee7e59" CollectType="MainAssetCollector" AddressRule="AddressByGroupAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
    <Group GroupActiveRule="EnableGroup" GroupName="Texture" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Texture/Emote" CollectGUID="1a6c4a37aace04c62881593ce8df0189" CollectType="MainAssetCollector" AddressRule="AddressByFolderAndFileName" PackRule="PackDirectory" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
  </Package>
  <Package PackageName="OtherPackage" PackageDesc="" AutoAddressable="True" SupportExtensionless="True" LocationToLower="False" IncludeAssetGUID="False" IgnoreRuleName="NormalIgnoreRule">
    <Group GroupActiveRule="EnableGroup" GroupName="ModelGroup" GroupDesc="" AssetTags="models" />
    <Group GroupActiveRule="EnableGroup" GroupName="SceneGroup" GroupDesc="" AssetTags="scenes" />
  </Package>
  <Package PackageName="Dlc1Package" PackageDesc="" AutoAddressable="True" SupportExtensionless="True" LocationToLower="False" IncludeAssetGUID="False" IgnoreRuleName="NormalIgnoreRule">
    <Group GroupActiveRule="EnableGroup" GroupName="ModelGroup" GroupDesc="" AssetTags="models" />
    <Group GroupActiveRule="EnableGroup" GroupName="SceneGroup" GroupDesc="" AssetTags="scenes" />
  </Package>
  <Package PackageName="Dlc2Package" PackageDesc="" AutoAddressable="True" SupportExtensionless="True" LocationToLower="False" IncludeAssetGUID="False" IgnoreRuleName="NormalIgnoreRule">
    <Group GroupActiveRule="EnableGroup" GroupName="ModelGroup" GroupDesc="" AssetTags="models" />
  </Package>
  <Package PackageName="DefaultPackageFirst" PackageDesc="" AutoAddressable="False" SupportExtensionless="True" LocationToLower="False" IncludeAssetGUID="False" IgnoreRuleName="NormalIgnoreRule">
    <Group GroupActiveRule="EnableGroup" GroupName="Default Group" GroupDesc="" AssetTags="">
      <Collector CollectPath="Assets/AssetRaw/Configs/default_package_xx.txt" CollectGUID="be7038693f11746d7950bb29960b2793" CollectType="MainAssetCollector" AddressRule="AddressByFileName" PackRule="PackRawFile" FilterRule="CollectAll" UserData="" AssetTags="" />
    </Group>
  </Package>
</root>