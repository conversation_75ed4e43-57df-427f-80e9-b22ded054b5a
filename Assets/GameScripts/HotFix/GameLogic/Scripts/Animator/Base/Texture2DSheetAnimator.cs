using UnityEngine;
using System;
using System.Collections.Generic;

/// <summary>
/// ✅ 优化版：Texture2DSheetAnimator（高性能合批版）
/// 特点：
/// - 完全保留原API兼容
/// - 改用 MaterialPropertyBlock 实现动画更新（不再生成材质副本）
/// - 支持 GPU Instancing / 批处理
/// - 支持 Flip、Overlay、Alpha 等功能
/// - 自动加载共享 ShaderGraph 材质
/// </summary>
public class Texture2DSheetAnimator
{
    // === Shader 属性缓存 ===
    private static readonly int TextureProp = Shader.PropertyToID("_Texture2D");
    private static readonly int ColumnsProp = Shader.PropertyToID("_Columns");
    private static readonly int RowsProp = Shader.PropertyToID("_Rows");
    private static readonly int FrameIndexProp = Shader.PropertyToID("_FrameIndex");
    private static readonly int FlipXProp = Shader.PropertyToID("_FlipX");
    private static readonly int OverlayTexProp = Shader.PropertyToID("_OverlayTexture");
    private static readonly int OverlayColorProp = Shader.PropertyToID("_OverlayColor");
    private static readonly int OverlayStrengthProp = Shader.PropertyToID("_OverlayStrength");
    private static readonly int AlphaProp = Shader.PropertyToID("_Alpha");

    // === 静态共享资源 ===
    private static Lazy<Material> _sharedUnlitMat = new Lazy<Material>(() =>
    {
        var mat = Resources.Load<Material>("Materials/UnlitSpriteSheet");
        if (mat == null)
        {
            mat = new Material(Shader.Find("Shader Graphs/UnlitSpriteSheetShaderGraph"));
            mat.enableInstancing = true;
        }
        return mat;
    });

    private static Lazy<Material> _sharedLitMat = new Lazy<Material>(() =>
    {
        var mat = Resources.Load<Material>("Materials/LitSpriteSheet");
        if (mat == null)
        {
            mat = new Material(Shader.Find("Shader Graphs/LitSpriteSheetShaderGraph"));
            mat.enableInstancing = true;
        }
        return mat;
    });

    private static Lazy<Texture2D> _transparentTex = new Lazy<Texture2D>(() =>
    {
        var tex = Resources.Load<Texture2D>("transparent");
        if (tex == null)
        {
            tex = Texture2D.blackTexture;
        }
        return tex;
    });

    // === 实例字段 ===
    private MeshRenderer _renderer;
    private MaterialPropertyBlock _propBlock;
    private bool _isLit = false;
    private bool _isPlaying = false;
    private bool _isTextureLoaded = false;
    private bool _flipX = false;
    private bool _newFlipX = false;

    private int _totalFrames = 0;
    private int _currentFrame = 0;
    private float _timer;

    public int columns { get; private set; } = 1;
    public int rows { get; private set; } = 1;

    public int startFrameIndex = 0;
    public int endFrameIndex = -1;
    public bool autoPlay = true;
    public bool loop = true;

    private float _fps = 8f;
    public float fps
    {
        get => _fps;
        set
        {
            if (value == 0)
            {
                Debug.LogError("fps 不能为 0");
                return;
            }
            _fps = value;
        }
    }

    public int frameWidth = 0;
    public int currentRow { get; private set; } = 0;
    public int currentColumn { get; private set; } = -1;
    public int currentFrameIndex => (currentColumn >= 0)
        ? (_currentFrame % rows)
        : (_currentFrame % columns);

    // === 初始化 ===
    public void SetMaterialInfo(Material material, Texture2D texture, int rows = 1, int columns = 1)
    {
        // ⚠️ 兼容原API，但现在 material 参数已无效
        // 实际使用 sharedMaterial + PropertyBlock 管理
        if (_renderer == null)
        {
            Debug.LogError("必须先调用 InitRenderer 绑定 MeshRenderer");
            return;
        }

        _propBlock.Clear();
        _isTextureLoaded = true;

        this.rows = rows;
        this.columns = columns;
        _totalFrames = rows * columns;

        texture.filterMode = FilterMode.Point;
        texture.wrapMode = TextureWrapMode.Clamp;

        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetTexture(TextureProp, texture);
        _propBlock.SetFloat(ColumnsProp, columns);
        _propBlock.SetFloat(RowsProp, rows);
        _propBlock.SetFloat(FrameIndexProp, 0);
        _renderer.SetPropertyBlock(_propBlock);

        if (autoPlay)
            Play();
    }

    /// <summary>
    /// 初始化渲染器（你必须手动调用一次）
    /// </summary>
    public void InitRenderer(MeshRenderer renderer, bool useLit)
    {
        _renderer = renderer;
        _propBlock = new MaterialPropertyBlock();
        _isLit = useLit;
        // _renderer.sharedMaterial = useLit ? _sharedLitMat.Value : _sharedUnlitMat.Value;
        _renderer.sharedMaterial = _sharedLitMat.Value;
        // 默认填充透明纹理，防止空白
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetTexture(TextureProp, _transparentTex.Value);
        _renderer.SetPropertyBlock(_propBlock);
    }

    public Material GetUnLitMaterial() => _sharedUnlitMat.Value;
    public Material GetLitMaterial() => _sharedLitMat.Value;

    // === 控制函数 ===
    public void Play()
    {
        if (!_isTextureLoaded)
        {
            Debug.LogWarning("Texture not loaded. Cannot play animation.");
            return;
        }
        _isPlaying = true;
    }

    public void Pause() => _isPlaying = false;

    public void Stop()
    {
        _isPlaying = false;
        _currentFrame = 0;
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetFloat(FrameIndexProp, _currentFrame);
        _renderer.SetPropertyBlock(_propBlock);
    }

    public void SetCurrentRow(int row)
    {
        if (row < 0 || row >= rows)
        {
            Debug.LogWarning($"Invalid row index: {row}. Must be between 0 and {rows - 1}");
            return;
        }
        currentRow = row;
        currentColumn = -1; // 切换到行播放模式
        _currentFrame = 0;
    }

    public void SetCurrentColumn(int column)
    {
        if (column < 0 || column >= columns)
        {
            Debug.LogWarning($"Invalid column index: {column}. Must be between 0 and {columns - 1}");
            return;
        }
        currentColumn = column;
        currentRow = 0;
        _currentFrame = 0;
    }

    public void SetFlipX(bool flipX)
    {
        _newFlipX = flipX;
        if (_newFlipX != _flipX)
        {
            _flipX = _newFlipX;
            _renderer.GetPropertyBlock(_propBlock);
            _propBlock.SetFloat(FlipXProp, _flipX ? 1 : 0);
            _renderer.SetPropertyBlock(_propBlock);
        }
    }

    public void SetOverlayTexture(Texture2D texture)
    {
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetTexture(OverlayTexProp, texture);
        _renderer.SetPropertyBlock(_propBlock);
    }

    public void SetOverlayColor(Color color)
    {
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetColor(OverlayColorProp, color);
        _renderer.SetPropertyBlock(_propBlock);
    }

    public void SetOverlayStrength(float strength)
    {
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetFloat(OverlayStrengthProp, strength);
        _renderer.SetPropertyBlock(_propBlock);
    }

    public void SetAlpha(float alpha)
    {
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetFloat(AlphaProp, alpha);
        _renderer.SetPropertyBlock(_propBlock);
    }

    public bool UpdateFrameIfNeed()
    {
        if (!_isTextureLoaded || !_isPlaying) return false;
    
        _timer += Time.deltaTime;
        if (_timer >= 1f / fps)
        {
            _timer = 0f;
            if(startFrameIndex >= 0 && endFrameIndex >= 0 && startFrameIndex > endFrameIndex) {
                Debug.LogError("startFrameIndex > endFrameIndex");
                return false;
            }
            if(startFrameIndex >= 0 && endFrameIndex >= 0 && endFrameIndex >= columns * rows) {
                Debug.LogError("endFrameIndex > columns * rows");
                return false;
            }
            SetFlipX(_newFlipX);
            if (currentColumn >= 0) // 按列播放模式
            {
                int newRow = currentRow + 1;
                if(newRow == endFrameIndex + 1 && startFrameIndex >= 0 && endFrameIndex >= 0) {
                    // newRow = startFrameIndex;
                    newRow = loop ? startFrameIndex : endFrameIndex;
                    _isPlaying = loop;
                }
                if (newRow >= rows)
                {
                    newRow = loop ? 0 : rows - 1;
                    _isPlaying = loop;
                }
                
                if (newRow != currentRow)
                {
                    int globalFrameIndex = newRow * columns + currentColumn;
                    currentRow = newRow;
                    if(globalFrameIndex < startFrameIndex && startFrameIndex >= 0) {
                        globalFrameIndex = startFrameIndex;
                    } else if(globalFrameIndex > endFrameIndex && endFrameIndex >= 0) {
                        globalFrameIndex = endFrameIndex;
                    }
                    // _targetMaterial.SetFloat("_FrameIndex", globalFrameIndex);
                    _renderer.GetPropertyBlock(_propBlock);
                    _propBlock.SetFloat(FrameIndexProp, globalFrameIndex);
                    _renderer.SetPropertyBlock(_propBlock);
                    return true;
                }
            }
            else // 按行播放模式(默认)
            {
                int newFrame = _currentFrame + 1;
                if(_currentFrame == endFrameIndex + 1 && startFrameIndex >= 0 && endFrameIndex >= 0) {
                    newFrame = loop ? startFrameIndex : endFrameIndex;
                    _isPlaying = loop;
                }
                int framesInRow = columns;
                
                if (newFrame >= framesInRow)
                {
                    newFrame = loop ? 0 : framesInRow - 1;
                    _isPlaying = loop;
                }
                
                if (newFrame != _currentFrame)
                {
                    int globalFrameIndex = currentRow * columns + newFrame;
                    _currentFrame = newFrame;
                    if(globalFrameIndex < startFrameIndex && startFrameIndex >= 0) {
                        globalFrameIndex = startFrameIndex;
                    } else if(globalFrameIndex > endFrameIndex && endFrameIndex >= 0) {
                        globalFrameIndex = endFrameIndex;
                    }
                    // globalFrameIndex = 2;
                    // _targetMaterial.SetFloat("_FrameIndex", globalFrameIndex);
                    _renderer.GetPropertyBlock(_propBlock);
                    _propBlock.SetFloat(FrameIndexProp, globalFrameIndex);
                    _renderer.SetPropertyBlock(_propBlock);
                    return true;
                }
            }
        }
        return false;
    }
}


// using UnityEngine;
// using UnityEngine.Networking;
// using System.Collections;
// using System.IO;
// using UnityEngine.UI;
// using System;

public class Texture2DSheetAnimatorOld
{
    // private static Texture2D transparentTexture = null;
    // private static Material _unlitSpriteSheet = null;
    // private static Material _litSpriteSheet = null;
    private static Lazy<Texture2D> transparentTexture = new Lazy<Texture2D>(() => Resources.Load<Texture2D>("transparent"));
    private static Lazy<Material> _unlitSpriteSheet = new Lazy<Material>(() => new Material(Shader.Find("Shader Graphs/UnlitSpriteSheetShaderGraph")));
    private static Lazy<Material> _litSpriteSheet = new Lazy<Material>(() => new Material(Shader.Find("Shader Graphs/LitSpriteSheetShaderGraph")));
    public int columns { get; private set; } = 1;
    public int rows { get; private set; } = 1;
    public int startFrameIndex = 0;
    public int endFrameIndex = -1;
    
     
    // [Header("动画帧配置")]
    public float fps {
        get {
            return _fps;
        }
        set {
            if(value == 0) {
                Debug.LogError("fps 不能为 0");
                return;
            }
            _fps = value;
        }
    }
    private float _fps = 8f;

    [Tooltip("每帧的宽度（像素），默认 0 表示自动计算为整张图高度")]
    public int frameWidth = 0;

    [Header("动画控制")]
    public bool autoPlay = true;
    public bool loop = true;

    [Header("绑定材质（使用 Shader Graph）")]
    private Material _targetMaterial;

    public int currentRow { get; private set; } = 0;
    public int currentColumn { get; private set; } = -1; // -1表示不按列播放
    private int _totalFrames = 0;
    private int _currentFrame = 0;
    public int currentFrameIndex { //当前行的第几帧 //因为只会播放一行
        get {
            if (currentColumn >= 0) {
                return _currentFrame % rows;
            } else {
                return _currentFrame % columns;
            }
        }
    }
    private float _timer;
    private bool _isPlaying = false;
    private bool _isTextureLoaded = false;
    void Awake()
    {
        // if(transparentTexture == null) {
        //     transparentTexture = Resources.Load<Texture2D>("transparent");
        // }
        // if(_unlitSpriteSheet == null) {
        //     _unlitSpriteSheet = new Material(Shader.Find("Shader Graphs/UnlitSpriteSheetShaderGraph"));
        // }
        // if(_litSpriteSheet == null) {
        //     _litSpriteSheet = new Material(Shader.Find("Shader Graphs/LitSpriteSheetShaderGraph"));
        // }
    }
    void Start()
    {
        // ✅ 自动获取挂载在当前物体上的材质
        // MeshRenderer renderer = GetComponent<MeshRenderer>();
        // if (renderer == null)
        // {
        //     Debug.LogError("Missing MeshRenderer on " + gameObject.name);
        //     return;
        // }
        // ✅ 实例化材质，避免影响其他对象
        // _targetMaterial = new Material(renderer.sharedMaterial);
        // _targetMaterial.SetTexture("_Texture2D", transparentTexture.Value);
        // renderer.material = _targetMaterial;
        // _totalFrames = columns * rows;
        // StartCoroutine(LoadTextureAndPlay());
    }
    
//     IEnumerator LoadTextureAndPlay()
//     {
//         _isLoadSpriteSheet = false;
//         if (string.IsNullOrEmpty(imageName)) {
//             yield break; // 如果没有图片名，直接返回，避免警告
//         }
//         string path = System.IO.Path.Combine(Application.streamingAssetsPath, imageName);

// #if UNITY_ANDROID && !UNITY_EDITOR
//     UnityWebRequest request = UnityWebRequestTexture.GetTexture(path);
//     yield return request.SendWebRequest();
//     if (request.result != UnityWebRequest.Result.Success)
//     {
//         Debug.LogError("Failed to load: " + request.error);
//         yield break; // 加上这个
//     }
//     Texture2D tex = DownloadHandlerTexture.GetContent(request);
// #else
//         if (!System.IO.File.Exists(path))
//         {
//             Debug.LogError("File not found: " + path);
//             yield break; // 非安卓也加一个退出
//         }

//         byte[] fileData = System.IO.File.ReadAllBytes(path);
//         Texture2D tex = new Texture2D(2, 2);
//         tex.LoadImage(fileData);
// #endif

//         // 设置像素风格
//         tex.filterMode = FilterMode.Point;
//         tex.wrapMode = TextureWrapMode.Clamp;
//         // 自动计算帧数（假设只有一行）
//         int width = tex.width;
//         int height = tex.height;

//         int frameW = (frameWidth > 0) ? frameWidth : height; // 如果未设置帧宽，则默认帧宽 = 高度（正方形帧）

//         columns = width / frameW;
//         rows = 1;
//         totalFrames = columns;
//         // 应用到材质
//         _targetMaterial.SetTexture("_Texture2D", tex);
//         _targetMaterial.SetFloat("_Columns", columns);
//         _targetMaterial.SetFloat("_Rows", rows);
//         _isLoadSpriteSheet = true;
//         yield return null; // ✅ 最后统一返回一个值，避免警告
//     }

    // void Update()
    // {
    //     if (!_isLoadSpriteSheet) return;

    //     timer += Time.deltaTime;
    //     if (timer >= 1f / fps)
    //     {
    //         timer = 0f;
    //         currentFrame = (currentFrame + 1) % totalFrames;
    //         _targetMaterial.SetFloat("_FrameIndex", currentFrame);
    //     }
    // }
    // public void SetFps(float fps) {
    //     this.fps = fps;
    // }
    public void InitRenderer(MeshRenderer renderer, bool useLit)
    {
        // _renderer = renderer;
        // _propBlock = new MaterialPropertyBlock();
        // _isLit = useLit;
        // _renderer.sharedMaterial = useLit ? _sharedLitMat.Value : _sharedUnlitMat.Value;

        // // 默认填充透明纹理，防止空白
        // _propBlock.SetTexture(TextureProp, _transparentTex.Value);
        // _renderer.SetPropertyBlock(_propBlock);
    }
    public Material GetUnLitMaterial() {
        Material material = new Material(_unlitSpriteSheet.Value);
        // SetMaterial(material, texture, rows, columns);
        return material;
        // material.SetTexture("_Texture2D", texture);
        // material.SetFloat("_Columns", columns);
    }
    public Material GetLitMaterial() {
        Material material = new Material(_litSpriteSheet.Value);
        // SetMaterial(material, texture, rows, columns);
        return material;
        // material.SetTexture("_Texture2D", texture);
        // material.SetFloat("_Columns", columns);
    }
    private bool _flipX = false;
    private bool _newflipX = false;
    public void SetFlipX(bool flipX) {
        _newflipX = flipX;
        if(_targetMaterial != null && _newflipX != _flipX) {
            _targetMaterial.SetFloat("_FlipX", _newflipX? 1 : 0);
            _flipX = _newflipX;
        }
    }
    public void SetOverlayTexture(Texture2D texture) {
        if(_targetMaterial != null) {
            _targetMaterial.SetTexture("_OverlayTexture", texture);
        }
    }
    public void SetOverlayColor(Color color) {
        if(_targetMaterial != null) {
            _targetMaterial.SetColor("_OverlayColor", color);
        }
    }
    public void SetOverlayStrength(float strength) {
        if(_targetMaterial != null) {
            _targetMaterial.SetFloat("_OverlayStrength", strength);
        }
    }
    public void SetAlpha(float alpha) {
        if(_targetMaterial != null) {
            _targetMaterial.SetFloat("_Alpha", alpha);
        }
    }
    public void SetMaterialInfo(Material material, Texture2D texture, int rows = 1, int columns = 1) {
        _targetMaterial = material;
        SetTexture(texture, rows, columns);
    }
    private void SetTexture(Texture2D texture, int rows = 1, int columns = 1)
    {
        if (texture == null || _targetMaterial == null)
        {
            Debug.LogError("Texture or _targetMaterial is null");
            return;
        }
        _totalFrames = rows * columns;
        _targetMaterial.SetTexture("_Texture2D", texture);
        
        // 设置像素风格
        texture.filterMode = FilterMode.Point;
        texture.wrapMode = TextureWrapMode.Clamp;

        // 自动计算帧数
        // int width = texture.width;
        // int height = texture.height;

        // int frameW = (frameWidth > 0) ? frameWidth : height;

        // columns = width / frameW;
        // rows = 1;
        this.rows = rows;
        this.columns = columns;
        // this.rows = 1;
        // this.columns = 1;
        _totalFrames = this.rows * this.columns;

        _targetMaterial.SetFloat("_Columns", columns);
        _targetMaterial.SetFloat("_Rows", rows);
        SetFlipX(false);
        SetFlipX(_flipX);
        _isTextureLoaded = true;
        if (autoPlay)
        {
            Play();
        }
    }

    public void Play()
    {
        if (!_isTextureLoaded)
        {
            Debug.LogWarning("Texture not loaded. Cannot play animation.");
            return;
        }
        _isPlaying = true;
    }

    public void Pause()
    {
        _isPlaying = false;
    }

    public void Stop()
    {
        _isPlaying = false;
        //reset
        // if(currentColumn < 0) {
        //     SetCurrentRow(0);
        // } else {
        //     SetCurrentColumn(0);
        // }
        _currentFrame = currentRow * columns;
        if(_isTextureLoaded) {
            _targetMaterial.SetFloat("_FrameIndex", _currentFrame);
        }
    }


    // 设置当前行
    public void SetCurrentRow(int row)
    {
        if (row < 0 || row >= rows)
        {
            Debug.LogWarning($"Invalid row index: {row}. Must be between 0 and {rows - 1}");
            return;
        }
        currentRow = row;
        currentColumn = -1; // 切换到行播放模式
        _currentFrame = 0;
    }

    // 新增：设置当前列
    public void SetCurrentColumn(int column)
    {
        if (column < 0 || column >= columns)
        {
            Debug.LogWarning($"Invalid column index: {column}. Must be between 0 and {columns - 1}");
            return;
        }
        currentColumn = column;
        currentRow = 0;
        _currentFrame = 0;
    }

    public bool UpdateFrameIfNeed()
    {
        if (!_isTextureLoaded || !_isPlaying) return false;
    
        _timer += Time.deltaTime;
        if (_timer >= 1f / fps)
        {
            _timer = 0f;
            if(startFrameIndex >= 0 && endFrameIndex >= 0 && startFrameIndex > endFrameIndex) {
                Debug.LogError("startFrameIndex > endFrameIndex");
                return false;
            }
            if(startFrameIndex >= 0 && endFrameIndex >= 0 && endFrameIndex >= columns * rows) {
                Debug.LogError("endFrameIndex > columns * rows");
                return false;
            }
            SetFlipX(_newflipX);
            if (currentColumn >= 0) // 按列播放模式
            {
                int newRow = currentRow + 1;
                if(newRow == endFrameIndex + 1 && startFrameIndex >= 0 && endFrameIndex >= 0) {
                    // newRow = startFrameIndex;
                    newRow = loop ? startFrameIndex : endFrameIndex;
                    _isPlaying = loop;
                }
                if (newRow >= rows)
                {
                    newRow = loop ? 0 : rows - 1;
                    _isPlaying = loop;
                }
                
                if (newRow != currentRow)
                {
                    int globalFrameIndex = newRow * columns + currentColumn;
                    currentRow = newRow;
                    if(globalFrameIndex < startFrameIndex && startFrameIndex >= 0) {
                        globalFrameIndex = startFrameIndex;
                    } else if(globalFrameIndex > endFrameIndex && endFrameIndex >= 0) {
                        globalFrameIndex = endFrameIndex;
                    }
                    _targetMaterial.SetFloat("_FrameIndex", globalFrameIndex);
                    return true;
                }
            }
            else // 按行播放模式(默认)
            {
                int newFrame = _currentFrame + 1;
                if(_currentFrame == endFrameIndex + 1 && startFrameIndex >= 0 && endFrameIndex >= 0) {
                    newFrame = loop ? startFrameIndex : endFrameIndex;
                    _isPlaying = loop;
                }
                int framesInRow = columns;
                
                if (newFrame >= framesInRow)
                {
                    newFrame = loop ? 0 : framesInRow - 1;
                    _isPlaying = loop;
                }
                
                if (newFrame != _currentFrame)
                {
                    int globalFrameIndex = currentRow * columns + newFrame;
                    _currentFrame = newFrame;
                    if(globalFrameIndex < startFrameIndex && startFrameIndex >= 0) {
                        globalFrameIndex = startFrameIndex;
                    } else if(globalFrameIndex > endFrameIndex && endFrameIndex >= 0) {
                        globalFrameIndex = endFrameIndex;
                    }
                    _targetMaterial.SetFloat("_FrameIndex", globalFrameIndex);
                    return true;
                }
            }
        }
        return false;
    }
}