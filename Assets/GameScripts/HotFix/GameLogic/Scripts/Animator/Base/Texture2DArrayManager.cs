using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 运行时Texture2DArray管理器，支持动态添加贴图、LRU缓存清理、多分辨率支持
/// </summary>
public class Texture2DArrayManager {

    /// <summary>
    /// 单个Texture2DArray的管理信息
    /// </summary>
    private class ArrayInfo {
        public Texture2DArray textureArray;
        public Dictionary<string, int> idToIndex = new Dictionary<string, int>();
        public Dictionary<int, string> indexToId = new Dictionary<int, string>();
        public Dictionary<string, float> lastAccessTime = new Dictionary<string, float>();
        public Queue<int> freeIndices = new Queue<int>();
        public int maxDepth;
        public int width;
        public int height;
        public TextureFormat format;

        public ArrayInfo(int width, int height, int maxDepth, TextureFormat format) {
            this.width = width;
            this.height = height;
            this.maxDepth = maxDepth;
            this.format = format;

            // 创建Texture2DArray
            textureArray = new Texture2DArray(width, height, maxDepth, format, false);

            // 初始化所有索引为可用
            for (int i = 0; i < maxDepth; i++) {
                freeIndices.Enqueue(i);
            }
        }

        /// <summary>
        /// 获取贴图在数组中的索引，如果不存在返回-1
        /// </summary>
        public int GetIndex(string id) {
            UpdateAccessTime(id);
            return idToIndex.TryGetValue(id, out int index) ? index : -1;
        }

        /// <summary>
        /// 添加贴图到数组中，返回分配的索引
        /// </summary>
        public int AddTexture(string id, Texture2D texture) {
            // 如果已存在，更新访问时间并返回现有索引
            if (idToIndex.TryGetValue(id, out int existingIndex)) {
                UpdateAccessTime(id);
                return existingIndex;
            }

            int targetIndex;

            // 如果有空闲位置，直接使用
            if (freeIndices.Count > 0) {
                targetIndex = freeIndices.Dequeue();
            } else {
                // 没有空闲位置，清理最久未使用的贴图
                targetIndex = EvictLeastRecentlyUsed();
            }

            // 将贴图复制到数组中
            Graphics.CopyTexture(texture, 0, 0, textureArray, targetIndex, 0);

            // 更新映射关系
            idToIndex[id] = targetIndex;
            indexToId[targetIndex] = id;
            UpdateAccessTime(id);

            return targetIndex;
        }

        /// <summary>
        /// 清理指定ID的贴图
        /// </summary>
        public bool RemoveTexture(string id) {
            if (!idToIndex.TryGetValue(id, out int index)) {
                return false;
            }

            // 清理映射关系
            idToIndex.Remove(id);
            indexToId.Remove(index);
            lastAccessTime.Remove(id);

            // 将索引加入空闲队列
            freeIndices.Enqueue(index);

            return true;
        }

        /// <summary>
        /// 清空所有贴图
        /// </summary>
        public void Clear() {
            idToIndex.Clear();
            indexToId.Clear();
            lastAccessTime.Clear();
            freeIndices.Clear();

            // 重新初始化空闲索引
            for (int i = 0; i < maxDepth; i++) {
                freeIndices.Enqueue(i);
            }
        }

        /// <summary>
        /// 更新访问时间
        /// </summary>
        private void UpdateAccessTime(string id) {
            lastAccessTime[id] = Time.time;
        }

        /// <summary>
        /// 清理最久未使用的贴图，返回被清理的索引
        /// </summary>
        private int EvictLeastRecentlyUsed() {
            string oldestId = null;
            float oldestTime = float.MaxValue;

            foreach (var kvp in lastAccessTime) {
                if (kvp.Value < oldestTime) {
                    oldestTime = kvp.Value;
                    oldestId = kvp.Key;
                }
            }

            if (oldestId != null) {
                int index = idToIndex[oldestId];
                RemoveTexture(oldestId);
                return index;
            }

            return 0; // 理论上不应该到达这里
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose() {
            if (textureArray != null) {
                UnityEngine.Object.DestroyImmediate(textureArray);
                textureArray = null;
            }
        }
    }

    // 不同分辨率的Texture2DArray管理
    private Dictionary<string, ArrayInfo> arrayInfos = new Dictionary<string, ArrayInfo>();

    // 默认配置
    private int defaultMaxDepth = 512;
    private TextureFormat defaultFormat = TextureFormat.RGBA32;

    /// <summary>
    /// 设置默认的数组深度
    /// </summary>
    public void SetDefaultMaxDepth(int maxDepth) {
        defaultMaxDepth = maxDepth;
    }

    /// <summary>
    /// 设置默认的贴图格式
    /// </summary>
    public void SetDefaultFormat(TextureFormat format) {
        defaultFormat = format;
    }

    /// <summary>
    /// 获取贴图在数组中的索引，如果不存在返回-1
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <param name="textureArray">输出对应的Texture2DArray</param>
    /// <returns>贴图在数组中的索引，不存在返回-1</returns>
    public int GetTextureIndex(string id, out Texture2DArray textureArray) {
        textureArray = null;

        foreach (var arrayInfo in arrayInfos.Values) {
            int index = arrayInfo.GetIndex(id);
            if (index >= 0) {
                textureArray = arrayInfo.textureArray;
                return index;
            }
        }

        return -1;
    }

    /// <summary>
    /// 添加贴图到对应分辨率的数组中
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <param name="texture">要添加的贴图</param>
    /// <param name="maxDepth">可选的数组深度，不指定则使用默认值</param>
    /// <returns>贴图在数组中的索引</returns>
    public int AddTexture(string id, Texture2D texture, int? maxDepth = null) {
        if (texture == null) {
            Debug.LogError($"尝试添加空贴图，ID: {id}");
            return -1;
        }

        string key = GetArrayKey(texture.width, texture.height, texture.format);

        // 如果对应分辨率的数组不存在，创建新的
        if (!arrayInfos.TryGetValue(key, out ArrayInfo arrayInfo)) {
            int depth = maxDepth ?? defaultMaxDepth;
            arrayInfo = new ArrayInfo(texture.width, texture.height, depth, texture.format);
            arrayInfos[key] = arrayInfo;
        }

        return arrayInfo.AddTexture(id, texture);
    }

    /// <summary>
    /// 获取指定ID的贴图对应的Texture2DArray
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <returns>对应的Texture2DArray，不存在返回null</returns>
    public Texture2DArray GetTextureArray(string id) {
        GetTextureIndex(id, out Texture2DArray textureArray);
        return textureArray;
    }

    /// <summary>
    /// 移除指定ID的贴图
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveTexture(string id) {
        foreach (var arrayInfo in arrayInfos.Values) {
            if (arrayInfo.RemoveTexture(id)) {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 清空指定分辨率的所有贴图
    /// </summary>
    /// <param name="width">宽度</param>
    /// <param name="height">高度</param>
    /// <param name="format">格式</param>
    public void ClearArray(int width, int height, TextureFormat format) {
        string key = GetArrayKey(width, height, format);
        if (arrayInfos.TryGetValue(key, out ArrayInfo arrayInfo)) {
            arrayInfo.Clear();
        }
    }

    /// <summary>
    /// 清空所有贴图数组
    /// </summary>
    public void ClearAll() {
        foreach (var arrayInfo in arrayInfos.Values) {
            arrayInfo.Clear();
        }
    }

    /// <summary>
    /// 获取指定分辨率的Texture2DArray，如果不存在则创建
    /// </summary>
    /// <param name="width">宽度</param>
    /// <param name="height">高度</param>
    /// <param name="format">格式</param>
    /// <param name="maxDepth">可选的数组深度</param>
    /// <returns>对应的Texture2DArray</returns>
    public Texture2DArray GetOrCreateArray(int width, int height, TextureFormat format, int? maxDepth = null) {
        string key = GetArrayKey(width, height, format);

        if (!arrayInfos.TryGetValue(key, out ArrayInfo arrayInfo)) {
            int depth = maxDepth ?? defaultMaxDepth;
            arrayInfo = new ArrayInfo(width, height, depth, format);
            arrayInfos[key] = arrayInfo;
        }

        return arrayInfo.textureArray;
    }

    /// <summary>
    /// 生成数组的唯一键
    /// </summary>
    private string GetArrayKey(int width, int height, TextureFormat format) {
        return $"{width}x{height}_{format}";
    }

    /// <summary>
    /// 获取所有数组的统计信息
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public string GetStatistics() {
        var stats = new System.Text.StringBuilder();
        stats.AppendLine("Texture2DArray管理器统计:");

        foreach (var kvp in arrayInfos) {
            var arrayInfo = kvp.Value;
            int usedSlots = arrayInfo.maxDepth - arrayInfo.freeIndices.Count;
            stats.AppendLine($"  {kvp.Key}: {usedSlots}/{arrayInfo.maxDepth} 已使用");
        }

        return stats.ToString();
    }

    /// <summary>
    /// 释放所有资源
    /// </summary>
    public void Dispose() {
        foreach (var arrayInfo in arrayInfos.Values) {
            arrayInfo.Dispose();
        }
        arrayInfos.Clear();
    }
}