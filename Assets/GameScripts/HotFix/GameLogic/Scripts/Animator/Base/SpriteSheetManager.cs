using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 精灵表管理器，结合Material和Texture2DArray提供统一的贴图加载接口
/// </summary>
public class SpriteSheetManager {
    
    /// <summary>
    /// 贴图请求结果
    /// </summary>
    public class TextureRequest {
        public Material material;
        public int arrayIndex;
        public int width;
        public int height;
        public bool isNewlyAdded;

        public TextureRequest(Material material, int arrayIndex, int width, int height, bool isNewlyAdded) {
            this.material = material;
            this.arrayIndex = arrayIndex;
            this.width = width;
            this.height = height;
            this.isNewlyAdded = isNewlyAdded;
        }
    }

    /// <summary>
    /// 贴图信息缓存
    /// </summary>
    private class TextureInfo {
        public int width;
        public int height;
        public bool isLit;

        public TextureInfo(int width, int height, bool isLit) {
            this.width = width;
            this.height = height;
            this.isLit = isLit;
        }
    }
    
    // 共享的Material实例
    private static Lazy<Material> _unlitSpriteSheet = new Lazy<Material>(() => new Material(Shader.Find("Shader Graphs/UnlitSpriteSheetShaderGraph")));
    private static Lazy<Material> _litSpriteSheet = new Lazy<Material>(() => new Material(Shader.Find("Shader Graphs/LitSpriteSheetShaderGraph")));
    
    // Texture2DArray管理器
    private Texture2DArrayManager arrayManager;

    // 贴图信息缓存，只存储尺寸等基本信息
    private Dictionary<string, TextureInfo> textureInfoCache = new Dictionary<string, TextureInfo>();

    // 记录每个Texture2DArray是否已经设置到对应的Material
    private HashSet<string> materialArraySet = new HashSet<string>();
    
    // 单例实例
    private static SpriteSheetManager _instance;
    public static SpriteSheetManager Instance {
        get {
            if (_instance == null) {
                _instance = new SpriteSheetManager();
            }
            return _instance;
        }
    }
    
    private SpriteSheetManager() {
        arrayManager = new Texture2DArrayManager();
        // 设置较大的默认深度以支持更多贴图
        arrayManager.SetDefaultMaxDepth(512);
    }
    
    /// <summary>
    /// 请求贴图资源，如果已存在则直接返回，否则需要外部提供贴图数据
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <param name="isLit">是否使用Lit材质</param>
    /// <returns>贴图请求结果，如果贴图不存在则arrayIndex为-1</returns>
    public TextureRequest RequestTexture(string id, bool isLit = false) {
        // 获取对应的Material
        Material material = isLit ? _litSpriteSheet.Value : _unlitSpriteSheet.Value;

        // 检查是否已经在数组中
        int arrayIndex = arrayManager.GetTextureIndex(id, out Texture2DArray textureArray);

        if (arrayIndex >= 0) {
            // 已存在，检查是否需要设置Texture2DArray到Material
            string arrayKey = GetArrayKey(textureArray, isLit);
            if (!materialArraySet.Contains(arrayKey)) {
                // 首次使用此数组，设置到Material
                material.SetTexture("_MainTexArray", textureArray);
                materialArraySet.Add(arrayKey);
            }

            // 设置当前贴图的索引
            material.SetInt("_TexIndex", arrayIndex);

            // 从缓存中获取贴图信息
            if (textureInfoCache.TryGetValue(id, out TextureInfo info)) {
                return new TextureRequest(material, arrayIndex, info.width, info.height, false);
            } else {
                // 理论上不应该到这里，但为了安全返回默认值
                return new TextureRequest(material, arrayIndex, 0, 0, false);
            }
        }

        // 不存在，返回Material但arrayIndex为-1，表示需要外部提供贴图
        return new TextureRequest(material, -1, 0, 0, false);
    }
    
    /// <summary>
    /// 添加贴图到管理器中
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <param name="texture">贴图数据</param>
    /// <param name="isLit">是否使用Lit材质</param>
    /// <param name="maxDepth">可选的数组深度</param>
    /// <returns>贴图请求结果</returns>
    public TextureRequest AddTexture(string id, Texture2D texture, bool isLit = false, int? maxDepth = null) {
        if (texture == null) {
            Debug.LogError($"尝试添加空贴图，ID: {id}");
            return new TextureRequest(isLit ? _litSpriteSheet.Value : _unlitSpriteSheet.Value, -1, 0, 0, false);
        }

        // 添加到数组管理器
        int arrayIndex = arrayManager.AddTexture(id, texture, maxDepth);

        // 缓存贴图信息（只存储尺寸等基本信息）
        textureInfoCache[id] = new TextureInfo(texture.width, texture.height, isLit);

        // 获取对应的Material和Texture2DArray
        Material material = isLit ? _litSpriteSheet.Value : _unlitSpriteSheet.Value;
        Texture2DArray textureArray = arrayManager.GetTextureArray(id);

        // 检查是否需要设置Texture2DArray到Material
        string arrayKey = GetArrayKey(textureArray, isLit);
        if (!materialArraySet.Contains(arrayKey)) {
            // 首次使用此数组，设置到Material
            material.SetTexture("_MainTexArray", textureArray);
            materialArraySet.Add(arrayKey);
        }

        // 设置当前贴图的索引
        material.SetInt("_TexIndex", arrayIndex);

        return new TextureRequest(material, arrayIndex, texture.width, texture.height, true);
    }
    
    /// <summary>
    /// 移除指定ID的贴图
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveTexture(string id) {
        bool removed = arrayManager.RemoveTexture(id);
        if (removed) {
            textureInfoCache.Remove(id);
        }
        return removed;
    }

    /// <summary>
    /// 清空所有贴图
    /// </summary>
    public void ClearAll() {
        arrayManager.ClearAll();
        textureInfoCache.Clear();
        materialArraySet.Clear();
    }

    /// <summary>
    /// 生成数组的唯一键，用于标识Material和Texture2DArray的组合
    /// </summary>
    private string GetArrayKey(Texture2DArray textureArray, bool isLit) {
        return $"{textureArray.GetInstanceID()}_{(isLit ? "Lit" : "Unlit")}";
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public string GetStatistics() {
        return $"缓存贴图信息数量: {textureInfoCache.Count}\n已设置Material数组: {materialArraySet.Count}\n{arrayManager.GetStatistics()}";
    }

    /// <summary>
    /// 释放所有资源
    /// </summary>
    public void Dispose() {
        arrayManager?.Dispose();
        textureInfoCache.Clear();
        materialArraySet.Clear();
        _instance = null;
    }
}
