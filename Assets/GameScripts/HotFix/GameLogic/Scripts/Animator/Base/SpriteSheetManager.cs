using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 精灵表管理器，结合Material和Texture2DArray提供统一的贴图加载接口
/// </summary>
public class SpriteSheetManager {
    
    /// <summary>
    /// 贴图请求结果
    /// </summary>
    public class TextureRequest {
        public Material material;
        public Texture2D texture;
        public int arrayIndex;
        public bool isNewlyAdded;
        
        public TextureRequest(Material material, Texture2D texture, int arrayIndex, bool isNewlyAdded) {
            this.material = material;
            this.texture = texture;
            this.arrayIndex = arrayIndex;
            this.isNewlyAdded = isNewlyAdded;
        }
    }
    
    // 共享的Material实例
    private static Lazy<Material> _unlitSpriteSheet = new Lazy<Material>(() => new Material(Shader.Find("Shader Graphs/UnlitSpriteSheetShaderGraph")));
    private static Lazy<Material> _litSpriteSheet = new Lazy<Material>(() => new Material(Shader.Find("Shader Graphs/LitSpriteSheetShaderGraph")));
    
    // Texture2DArray管理器
    private Texture2DArrayManager arrayManager;
    
    // 贴图缓存，存储已加载的贴图
    private Dictionary<string, Texture2D> textureCache = new Dictionary<string, Texture2D>();
    
    // 单例实例
    private static SpriteSheetManager _instance;
    public static SpriteSheetManager Instance {
        get {
            if (_instance == null) {
                _instance = new SpriteSheetManager();
            }
            return _instance;
        }
    }
    
    private SpriteSheetManager() {
        arrayManager = new Texture2DArrayManager();
        // 设置较大的默认深度以支持更多贴图
        arrayManager.SetDefaultMaxDepth(512);
    }
    
    /// <summary>
    /// 请求贴图资源，如果已存在则直接返回，否则需要外部提供贴图数据
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <param name="isLit">是否使用Lit材质</param>
    /// <returns>贴图请求结果，如果贴图不存在则texture为null</returns>
    public TextureRequest RequestTexture(string id, bool isLit = false) {
        // 获取对应的Material
        Material material = isLit ? _litSpriteSheet.Value : _unlitSpriteSheet.Value;
        
        // 检查是否已经在数组中
        int arrayIndex = arrayManager.GetTextureIndex(id, out Texture2DArray textureArray);
        
        if (arrayIndex >= 0) {
            // 已存在，更新Material的Texture2DArray属性
            UpdateMaterialTextureArray(material, textureArray);
            
            // 从缓存中获取原始贴图
            textureCache.TryGetValue(id, out Texture2D cachedTexture);
            
            return new TextureRequest(material, cachedTexture, arrayIndex, false);
        }
        
        // 不存在，返回Material但texture为null，表示需要外部提供贴图
        return new TextureRequest(material, null, -1, false);
    }
    
    /// <summary>
    /// 添加贴图到管理器中
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <param name="texture">贴图数据</param>
    /// <param name="isLit">是否使用Lit材质</param>
    /// <param name="maxDepth">可选的数组深度</param>
    /// <returns>贴图请求结果</returns>
    public TextureRequest AddTexture(string id, Texture2D texture, bool isLit = false, int? maxDepth = null) {
        if (texture == null) {
            Debug.LogError($"尝试添加空贴图，ID: {id}");
            return new TextureRequest(isLit ? _litSpriteSheet.Value : _unlitSpriteSheet.Value, null, -1, false);
        }
        
        // 添加到数组管理器
        int arrayIndex = arrayManager.AddTexture(id, texture, maxDepth);
        
        // 缓存原始贴图
        textureCache[id] = texture;
        
        // 获取对应的Material和Texture2DArray
        Material material = isLit ? _litSpriteSheet.Value : _unlitSpriteSheet.Value;
        Texture2DArray textureArray = arrayManager.GetTextureArray(id);
        
        // 更新Material的Texture2DArray属性
        UpdateMaterialTextureArray(material, textureArray);
        
        return new TextureRequest(material, texture, arrayIndex, true);
    }
    
    /// <summary>
    /// 移除指定ID的贴图
    /// </summary>
    /// <param name="id">贴图ID</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveTexture(string id) {
        bool removed = arrayManager.RemoveTexture(id);
        if (removed) {
            textureCache.Remove(id);
        }
        return removed;
    }
    
    /// <summary>
    /// 清空所有贴图
    /// </summary>
    public void ClearAll() {
        arrayManager.ClearAll();
        textureCache.Clear();
    }
    
    /// <summary>
    /// 更新Material的Texture2DArray属性
    /// </summary>
    private void UpdateMaterialTextureArray(Material material, Texture2DArray textureArray) {
        if (material != null && textureArray != null) {
            // 假设shader中的Texture2DArray属性名为"_TextureArray"
            material.SetTexture("_TextureArray", textureArray);
        }
    }
    
    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public string GetStatistics() {
        return $"缓存贴图数量: {textureCache.Count}\n{arrayManager.GetStatistics()}";
    }
    
    /// <summary>
    /// 释放所有资源
    /// </summary>
    public void Dispose() {
        arrayManager?.Dispose();
        textureCache.Clear();
        _instance = null;
    }
}
