using System;
using System.Collections;
using System.Threading.Tasks;
using PetsServices;
using UnityEngine;
using UnityEngine.UI;

public class ImageSpriteAnimator : MonoBehaviour, IBaseAnimator
{
    public enum ContentMode {
        Fill,
        ScaleAspectFit
    }
    // private Lazy<SpriteAnimator> _lazySpriteAnimator = new Lazy<SpriteAnimator>(() => new SpriteAnimator());
    // protected ISpriteAnimator _spriteAnimator => _lazySpriteAnimator.Value;
    protected ISpriteAnimator _spriteAnimator;
    // private Texture2DSheetAnimator _texture2DSheetAnimator;
    // public Texture2DSheetInfo? info { get; private set; } 
    // public Sprite[] Sprites;            // 要播放的Sprite数组
    // public float PlaySpeed = 0.2f;       // 每帧的间隔时间，控制播放速度
    // public bool IsOriginImageSize = true;
    private ContentMode _contentMode = ContentMode.Fill;
    public ContentMode contentMode {
        get => _contentMode;
        set {
            _contentMode = value;
            RefreshContentMode();
        }
    }
    // private int _imageIndex = 0;         // 当前播放的Sprite索引
    private Image _imageComponent;       // 挂载的Image组件
    private Vector2 _imageOriginSize;       // 挂载的Image组件
    private Coroutine _playCoroutine;    // 控制动画播放的协程
    // public float playSpeed {//(秒/帧)
    //     get => 1f / _texture2DSheetAnimator.fps;
    //     set => _texture2DSheetAnimator.fps = 1f / value;
    // }
    void Awake()
    {
        // _texture2DSheetAnimator = new();
        // 获取挂载的Image组件
        _imageComponent = GetComponent<Image>();
        _imageOriginSize = _imageComponent.GetComponent<RectTransform>().rect.size;
        
        _spriteAnimator = new SpriteAnimator(_imageComponent.GetComponent<MeshRenderer>());
        //  TrainerResourceInfo info = new();
        // //  info.Status = NinCharacterStatus.Battle;
        // info.Name = "01";
        // info.Gender = "f";
        // // info.Type = AnimationSpriteResourceType.Line;
        // var result = await info.LoadAnimationTexture2D();
        // if(result.Success) {
        //     _imageComponent.material = _texture2DSheetAnimator.GetUnLitMaterial(result.Result.texture2D, result.Result.rows, result.Result.columns);
        //     // var targetMaterial = new Material(renderer.sharedMaterial);x
        //     // targetMaterial.SetTexture("_Texture2D", transparentTexture);
        //     // _imageComponent.material.SetTexture("_Texture2D", result.Result.texture2D);
        // }
        // var result = await info.LoadAnimationSprites();
        // if(result.Success) {
        //     var sprites = result.Result.sprites;
        //     if(sprites!= null) {
        //         // SetSprites(sprites[0]);
        //         // Debug.Log("trainerFullName:" + trainerFullName);
        //     }
        // }
    }

    // 开始播放动画
    // public void Play()
    // {
    //     _texture2DSheetAnimator.Play();
    //     // if (Sprites == null || Sprites.Length == 0)
    //     // {
    //     //     Debug.LogWarning("No sprites assigned for animation.");
    //     //     return;
    //     // }
        
    //     // Stop(); // 先停止当前播放，确保重新开始从第一个帧
    //     // _playCoroutine = StartCoroutine(PlayAnimation());
    // }

    // 暂停动画
    // public void Pause()
    // {
    //     _texture2DSheetAnimator.Pause();
    //     // if (_playCoroutine != null)
    //     // {
    //     //     StopCoroutine(_playCoroutine);
    //     //     _playCoroutine = null;
    //     // }
    // }

    // 停止动画并重置到第一帧
    // public void Stop()
    // {
    //     _texture2DSheetAnimator.Stop();
    //     // if (_playCoroutine != null)
    //     // {
    //     //     StopCoroutine(_playCoroutine);
    //     //     _playCoroutine = null;
    //     // }

    //     // // 重置到第一个Sprite
    //     // _imageIndex = 0;
    //     // if (Sprites != null && Sprites.Length > 0)
    //     // {
    //     //     _imageComponent.sprite = Sprites[_imageIndex];
    //     // }
    // }

    // 设置新的Sprites数组，并重新开始动画
    // public void SetSprites(Sprite[] newSprites)
    // {
    //     Stop();
    //     Sprites = newSprites;
    //     _imageIndex = 0; // 重置索引
    //     Play();          // 重新播放
    // }

    // row代表的是只循环的行数，从0开始
    // 比如row=1，代表只循环第2行的动画
    public void SetTexture(Texture2DSheetInfo info, int row = 0) {
        // _imageComponent.material = _spriteAnimator.GetSheetInfoMaterial(false, info, row);
        RefreshContentMode();
        Play();
    }
    private void RefreshContentMode() {
        return;
        if(_spriteAnimator.texture2DSheetInfo == null) {
            return;
        }
        var info = _spriteAnimator.texture2DSheetInfo.Value;
        if(contentMode == ContentMode.Fill) {
            RectTransform rectTransform = _imageComponent.GetComponent<RectTransform>();
            rectTransform.sizeDelta = _imageOriginSize;
        } else if(contentMode == ContentMode.ScaleAspectFit) {
            if(info.texture2D != null && info.cellSize != Vector2.zero) {
                Vector2 containerSize = _imageOriginSize;
                Vector2 contentSize = info.cellSize;
                
                // 计算适合容器的比例
                float widthRatio = containerSize.x / contentSize.x;
                float heightRatio = containerSize.y / contentSize.y;
                float scaleRatio = Mathf.Min(widthRatio, heightRatio);
                
                // 应用缩放后的尺寸
                Vector2 scaledSize = new Vector2(
                    contentSize.x * scaleRatio,
                    contentSize.y * scaleRatio
                );
                
                RectTransform rectTransform = _imageComponent.GetComponent<RectTransform>();
                rectTransform.sizeDelta = scaledSize;
            }
        }
        // this.contentMode = contentMode;
        
    }

    public void Play()
    {
        _spriteAnimator.Play();
    }

    public void Pause()
    {
        _spriteAnimator.Pause();
    }

    public void Stop()
    {
        _spriteAnimator.Stop();
    }
    public void Clear() {
        _spriteAnimator.Clear();
    }
    // 播放动画的协程
    // private IEnumerator PlayAnimation()
    // {
    //     while (true)
    //     {
    //         _imageComponent.sprite = Sprites[_imageIndex];

    //         if(IsOriginImageSize) {
    //             // 获取Sprite的原始尺寸
    //             float width = _imageComponent.sprite.rect.width;// / _imageComponent.sprite.pixelsPerUnit;
    //             float height = _imageComponent.sprite.rect.height;// / _imageComponent.sprite.pixelsPerUnit;

    //             // 设置Image的RectTransform尺寸
    //             RectTransform rectTransform = _imageComponent.GetComponent<RectTransform>();
    //             var ra =  2.5f;
    //             width = width * ra > _imageOriginSize.x ? _imageOriginSize.x : width * ra;
    //             height = height * ra > _imageOriginSize.y ? _imageOriginSize.y : height * ra;
    //             rectTransform.sizeDelta = new Vector2(width, height);
    //         }
    //         _imageIndex = (_imageIndex + 1) % Sprites.Length;
    //         yield return new WaitForSeconds(PlaySpeed);
    //     }
    // }
    void Update()
    {
        _spriteAnimator.UpdateAnimation();
    }
}