using System.Collections.Generic;
using UnityEngine;
using Role;
using System;
using Cysharp.Threading.Tasks;
using System.Threading;

public class NinSpriteAnimator : MonoBehaviour, IBaseAnimator
{
    // private SpriteAnimator _spriteAnimator;
    // private Lazy<SpriteAnimator> _lazySpriteAnimator = new Lazy<SpriteAnimator>(() => new SpriteAnimator());
    // protected ISpriteAnimator _spriteAnimator => _lazySpriteAnimator.Value;
    protected ISpriteAnimator _spriteAnimator;
    protected ISpriteAnimator spriteAnimator {
        get {
            if(_spriteAnimator == null) {
                _spriteAnimator = new SpriteAnimator(_meshRenderer);
            }
            return _spriteAnimator;
        }
    }
    protected bool _resourceInfoLoaded = false;
    protected float _originLocalScaleX;
    protected Vector3 _originLocalPosition;
    protected float _originScaleToWidth = 60;
    private bool autoRefreshBattlePokeContentSize = false;
    public bool autoRefreshFollowPokeContentSize = false;
    public Action<Vector2> infoLoadComplete;

    public float originScaleToWidthRatio {
        get {
            return _originScaleToWidth/_originLocalScaleX;
        }
    }
    // Texture2DSheetAnimator _texture2DSheetAnimator;
    // private Sprite[] _sprites = {};
    // // private Sprite[] battleCharacterSprites = new Sprite[0];
    // // public IAnimationSpriteResourceInfo spriteResourceInfo;
    // // public string CharacterFullPath; // 角色名称（用于加载特定的角色图片）
    // // public bool IsPokemon;
    void Awake()
    {
        _originLocalScaleX = _meshRenderer.gameObject.transform.localScale.x;
        _originLocalPosition = _meshRenderer.gameObject.transform.localPosition;
    }
    protected MeshRenderer _meshRenderer {
        get {
            return GetComponent<MeshRenderer>();
        }
    }

    protected IAnimationSpriteResourceInfo _spriteResourceInfo;
    public IAnimationSpriteResourceInfo SpriteResourceInfo {
        get {
            return _spriteResourceInfo;
        }
        set {
            _spriteResourceInfo = value;
            SetSpriteResourceInfo(_spriteResourceInfo).Forget();
        }
    }
    protected virtual void SetCurrentColumn(int column) {
        if(SpriteResourceInfo != null) {
            spriteAnimator.SetCurrentColumn(column);
        }
    }
    protected virtual void SetCurrentRow(int row) {
        if(SpriteResourceInfo != null) {
            spriteAnimator.SetCurrentRow(row);
        }
    }
    public virtual async UniTask SetSpriteResourceInfo(IAnimationSpriteResourceInfo info) {
        _spriteResourceInfo = info;
        await LoadCharacterSprites(default);
    }
    protected virtual async UniTask LoadCharacterSprites(CancellationToken token = default)
    {
        if(SpriteResourceInfo == null) {
            return;
        }
        var result = await SpriteResourceInfo.LoadAnimationTexture2D(token);
        _resourceInfoLoaded = result.Success;
        if(result.Success) {
            SetTexture(result.Content, spriteAnimator.CurrentRowAndColumn.row, spriteAnimator.CurrentRowAndColumn.column);
            if(autoRefreshBattlePokeContentSize) {
                RefreshBattlePokeContentSize();
            } else if(autoRefreshFollowPokeContentSize) {
                RefreshFollowPokeContentSize();
            }
            infoLoadComplete?.Invoke(InfoTextureSize());
        }
    }
    public virtual void RefreshBattlePokeContentSize()
    {
        if (spriteAnimator.texture2DSheetInfo == null)
            return;

        var info = spriteAnimator.texture2DSheetInfo.Value;

        if (info.texture2D != null && info.cellSize != Vector2.zero)
        {
            Vector2 cellSize = info.cellSize;

            float scaleX =  cellSize.x / originScaleToWidthRatio;
            float scaleY = cellSize.y / originScaleToWidthRatio;

            // 应用缩放（保留 z）
            transform.localScale = new Vector3(scaleX, scaleY, 1f);
        }
    }
    public virtual Vector2 InfoTextureSize() {
        if (spriteAnimator.texture2DSheetInfo == null)
            return Vector2.zero;

        var info = spriteAnimator.texture2DSheetInfo.Value;

        if (info.texture2D != null && info.cellSize != Vector2.zero)
        {
            return info.cellSize;
        }
        return Vector2.zero;
    }

    public virtual void RefreshFollowPokeContentSize()
    {
        if (spriteAnimator.texture2DSheetInfo == null)
            return;

        var info = spriteAnimator.texture2DSheetInfo.Value;

        if (info.texture2D != null && info.cellSize != Vector2.zero)
        {
            Vector2 cellSize = info.cellSize;
            var originScaleToWidth = 64;
            var originScale = _originLocalScaleX;
            float scaleX =  cellSize.x / originScaleToWidth * originScale;
            float scaleY = cellSize.y / originScaleToWidth * originScale;

            // 应用缩放（保留 z）
            transform.localScale = new Vector3(scaleX, scaleY, 1f);
            transform.localPosition = new Vector3(_originLocalPosition.x, _originLocalPosition.y * cellSize.x / originScaleToWidth, _originLocalPosition.z);
        }
    }

    public virtual void SetHidden(bool hidden) {
        if(hidden) {
            _meshRenderer.enabled = false;
        } else {
            _meshRenderer.enabled = true;
        }
    }
    // protected SpriteRenderer spriteRenderer;
    // protected float animationSpeed = 0.1f;
    // protected float defaultAnimationSpeed = 0.1f;
    // protected float timer;
    // protected int frameIndex = 0;
    // public bool IsAutoPlay = true;
    // protected bool isPaused = false;
    // protected bool isLooping = true;
    // protected int frameCount = 0;

    // protected float moveSpeed = 2.0f;

    // public Action<int> UpdateAnimationAction;
    // // protected Vector2 direction;
    // // public NinCharacterStatus Status;

    // void Awake()
    // {
    //     // _spriteAnimator = new SpriteAnimator();
    //     // _meshRenderer = GetComponent<MeshRenderer>();
    //     // spriteRenderer = GetComponent<SpriteRenderer>();
    //     // LoadCharacterSprites();
    // }
    public virtual void SetTexture(Texture2DSheetInfo info, int defaultRow = 0, int defaultColumn = -1) {    
        // if(_meshRenderer == null) {
        //     _meshRenderer = GetComponent<MeshRenderer>();
        //     if(_meshRenderer == null) {
        //         return;
        //     }
        // }
        if(_meshRenderer.material != null && _meshRenderer.material.name != "LitSpriteSheet") {
            // UnityEngine.Object.Destroy(_meshRenderer.material);
            _meshRenderer.material = spriteAnimator.GetSheetInfoMaterial(true, info, defaultRow, defaultColumn);
        } else {
            spriteAnimator.SetMaterialSheetInfo(null, info, defaultRow, defaultColumn);
        }
        // 先清理旧的Material
        // if(_meshRenderer.material != null && _meshRenderer.material.name != "LitSpriteSheet") {
        //     UnityEngine.Object.Destroy(_meshRenderer.material);
        //     _meshRenderer.material = _spriteAnimator.GetSheetInfoMaterial(true, info, defaultRow, defaultColumn);
        // } else {
        //     _spriteAnimator.SetMaterialSheetInfo(_meshRenderer.material, info, defaultRow, defaultColumn);
        // }
        spriteAnimator.Play();
    }
    public virtual void Clear() {
        spriteAnimator.Stop();
        SetTexture(DefaultResourceInfo.UnknownTexture2DSheetInfo);
    }
    public virtual void Play()
    {
        if(spriteAnimator!= null) {
            spriteAnimator.Play();
        }
    }

    public virtual void Pause()
    {
        if(spriteAnimator!= null) {
            spriteAnimator.Pause();
        }
    }

    public virtual void Stop()
    {
        if(spriteAnimator!= null) {
            spriteAnimator.Stop();
        }
    }
    public virtual void SetFlipX(bool flipX) {
        if(spriteAnimator!= null) {
            spriteAnimator.SetFlipX(flipX);
        }
    }
    void Update()
    {
        spriteAnimator.UpdateAnimation();
    }
    // protected virtual void Update()
    // {
    //     if (!isPaused)
    //     {
    //         UpdateAnimation();
    //         // MoveCharacter();
    //     }
    // }


    // public void SetSprites(Sprite[] _sprites) {
    //     var paused = isPaused;
    //     StopAnimation();
    //     this._sprites = _sprites;
    //     StopAnimation();//初始为0
    //     frameCount = _sprites.Length;
    //     if(paused != false) {
    //         StartAnimationIfNeed();
    //     }
    // }
    // // public void ChangeStatus(NinCharacterStatus status)
    // // {
    // //     if (Status == status) return;
    // //     Status = status;
    // //     StopAnimation();
    // //     spriteResourceInfo.Status = status;
    // //     LoadCharacterSprites();
    // //     // LoadCharacterSprites(CharacterFullPath + status.ToString());
    // // }

    // void UpdateAnimation()
    // {
    //     if(_sprites.Length <= 0) {
    //         return;
    //     }
    //     timer += Time.deltaTime;
    //     if (timer >= animationSpeed)
    //     {
    //         timer = 0f;
    //         frameIndex++;

    //         if (frameIndex >= frameCount)
    //         {
    //             frameIndex = isLooping ? 0 : frameCount - 1;
    //             if (!isLooping)
    //                 StopAnimation();
    //         }
    //     }
    //     spriteRenderer.sprite = _sprites[frameIndex];
    //     if(UpdateAnimationAction != null) {
    //         UpdateAnimationAction(frameIndex);
    //     }
    // }

    // // void MoveCharacter()
    // // {
    // //     transform.Translate(direction * moveSpeed * Time.deltaTime);
    // // }

    // public void StartAnimationIfNeed(bool isLooping = true)
    // {
    //     this.isLooping = isLooping;
    //     if (isPaused)
    //     {
    //         isPaused = false;
    //         timer = 0f;
    //     }
    // }
    // //停止贴图切换动画
    // public void StopAnimation()
    // {
    //     // UnityEngine.Debug.Log("StopAnimation");
    //     isPaused = true;
    //     frameIndex = 0;
    //     if(_sprites.Length > 0) {
    //         spriteRenderer.sprite = _sprites[0];
    //     }
    // }
    // //暂停贴图切换动画
    // public void PauseAnimation()
    // {
    //     // UnityEngine.Debug.Log("PauseAnimation");
    //     isPaused = true;
    // }

    // public void ResumeAnimation()
    // {
    //     if (isPaused)
    //     {
    //         isPaused = false;
    //     }
    // }

    // public void SetAnimationSpeedRatio(float speedRatio = 1.0f)
    // {
    //     animationSpeed = defaultAnimationSpeed / speedRatio;
    // }

    // public void SetAnimationSpeed(float newAnimationSpeed)
    // {
    //     if (newAnimationSpeed > 0)
    //     {
    //         animationSpeed = newAnimationSpeed;
    //     }
    //     else
    //     {
    //         Debug.LogWarning("Animation speed must be greater than 0!");
    //     }
    // }

    // private void LoadCharacterSprites()
    // {
    //     // StartCoroutine(spriteResourceInfo.LoadAnimationSprites((result) =>
    //     // {
    //     //     StopAnimation();//加载完成要切换资源，所以停止动画
    //     //     if (result.Result != null)
    //     //     {
    //     //         for (int i = 0; i < result.Result.Count; i++)
    //     //         {
    //     //             NinCharacterAnimatorDirection direction = (NinCharacterAnimatorDirection)i;
    //     //             if (!characterSprites.ContainsKey(direction))
    //     //                 characterSprites[direction] = result.Result[i];
    //     //         }
    //     //     }
    //     // }));
    //     // spriteResourceInfo.LoadAnimationSprites()
    //     // var sprites = ResourceLoad.LoadMapAnimationSprites(characterName);
    //     // for (int i = 0; i < sprites.Count; i++)
    //     // {
    //     //     NinCharacterAnimatorDirection direction = (NinCharacterAnimatorDirection)i;
    //     //     if (!characterSprites.ContainsKey(direction))
    //     //         characterSprites[direction] = sprites[i];
    //     // }
    // }

    // public void ChangeDirectionIfNeed(Direction direction, bool force = false)
    // {
    //     var animatorDirection = AnimatorDirectionByDirection(direction);
    //     if (force || currentAnimatorDirection != animatorDirection)
    //     {
    //         SetCurrentAnimatorDirection(animatorDirection);
    //     }
    // }

    // private NinCharacterAnimatorDirection AnimatorDirectionByDirection(Direction direction)
    // {
    //     return direction switch
    //     {
    //         Direction.N => NinCharacterAnimatorDirection.Up,
    //         Direction.NE or Direction.E or Direction.SE => NinCharacterAnimatorDirection.Right,
    //         Direction.S => NinCharacterAnimatorDirection.Down,
    //         Direction.SW or Direction.W or Direction.NW => NinCharacterAnimatorDirection.Left,
    //         _ => NinCharacterAnimatorDirection.Down
    //     };
    // }

    // private void SetCurrentAnimatorDirection(NinCharacterAnimatorDirection animatorDirection)
    // {
    //     if (characterSprites.ContainsKey(animatorDirection) && characterSprites[animatorDirection].Length > 0)
    //     {
    //         currentAnimatorDirection = animatorDirection;
    //         StopAnimation();
    //         StartAnimationIfNeed();
    //     }
    //     else
    //     {
    //         Debug.LogWarning("Character sprite for the current direction is missing or invalid.");
    //     }
    // }
}