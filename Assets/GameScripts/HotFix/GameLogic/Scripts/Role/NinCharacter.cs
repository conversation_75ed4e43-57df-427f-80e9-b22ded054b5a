using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using MoreMountains.TopDownEngine;
using System;
using PetsServices.Net;
using PetsServices.Store;
using Google.Protobuf;
using System.Linq;
using UnityEngine.AI;
using PokeApiNet;
using System.Threading.Tasks;
using PimDeWitte.UnityMainThreadDispatcher;
using PetsServices;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.CompilerServices;
using Role;
// 角色方向枚举
// public enum CharacterDirection
// {
//     Down,
//     Left,
//     Right,
//     Up
// }

public class NinCharacter : CharacterAbility, INinCharacterFollowObject
{
    public enum NinCharacterType {
        Player,
        Npc,
        Poke
    }
    public class NinSpeed {
        public const float walk = 3;
        public const float run = 4;
        // public const float bikeWalk = 3.5f;
        // public const float bikeRun = 4.5f;
        public const float ridWalk = 4f;
        public const float ridRun = 5;
    }
    public string characterName {  get; private set; }
    public Character TECharacter;
    public NinCharacterType ninCharacterType { get; private set; }
    public GameLogic.UI.Dialogue.NinDialogueZone dialogueZone;
    public NinSmartNavFollower smartNavFollower;
    // public NinCharacterPathfindToMouse3D ninCharacterPathfindToMouse3D;
    public NinCharacterCheckCube ninCharacterCheckCube;
    public NinCharacterFootsteps ninCharacterFootsteps;
    public NinChatacterTransfer ninChatacterTransfer;
    // private NinEnvironmentChecker _environmentChecker;
    // public Character gameCreatorCharacter;
    public NinCharacterAnimator animator;
    private Int64 _lastLocLineTs;
    // public info
    public TopDownController3D topDownController3D;
    public bool IsLocalTest = true;
    public MainServer.Trainer trainer { get; private set; }
    // private NinCharacter _followCharacter;
    public NinCharacterTopUI ninCharacterTopUI;
    // private Pokemon pokemon;
    private NinNetPokeInfo ninNetPokeInfo;
    private Vector3 _lastUpPosition;
    private CharacterRun _characterRun;
    private MainServer.TrainerLocStatus _locStatus = MainServer.TrainerLocStatus.LocStatusNone;
    private int _baseMoveSpeed = 0;//用于调试的时候增加速度
    private bool _isRun = false;
    public bool isRun {
        get {
            return _isRun;
        }
        set {
            _isRun = value;
            ninCharacterFootsteps.SetIsRun(_isRun);
        }
    }
    private bool _isSurf = false;
    public bool isSurf {
        get {
            return _isSurf;
        }
        set {
            if(_isSurf == value) {
                return;
            }
            _isSurf = value;
            if(_isSurf) {
                animator.ChangeStatusIfNeed(NinCharacterStatus.Surf);
            } else {
                if(IsRide()) {
                    //
                }
                animator.ChangeStatusIfNeed(NinCharacterStatus.Normal);
            }
        }
    }
    public MainServer.TrainerLocStatus locStatus {
        get {
            return _locStatus;
        }
        set {
            _locStatus = value;
            Debug.Log($"locStatus {value}");
            // switch (value)
            // {
            //     case MainServer.TrainerLocStatus.LocStatusNone:
            //         ninCharacterFootsteps.SetIsRun(false);
            //         break;
            //     case MainServer.TrainerLocStatus.LocStatusBattle:
            //         ninCharacterFootsteps.SetIsRun(false);
            //         break;
            //     case MainServer.TrainerLocStatus.LocStatusFishing:
            //         ninCharacterFootsteps.SetIsRun(false);
            //         break;
            //     case MainServer.TrainerLocStatus.LocStatusRun:
            //         ninCharacterFootsteps.SetIsRun(true);
            //         break;
            //     case MainServer.TrainerLocStatus.LocStatusSurf:
            //         ninCharacterFootsteps.SetIsRun(false);
            //         break;
            //     case MainServer.TrainerLocStatus.LocStatusRemove:
            //         ninCharacterFootsteps.SetIsRun(false);
            //         break;
            // }
            // animator.ChangeStatusIfNeed(value);
        }
    }
    // private NinSpeedType _speedType = NinSpeedType.Normal;
    // private bool isRidingPoke = false;
    // public MainServer.RoleDutyType roleDuty = MainServer.RoleDutyType.RoleNormal;
    public MainServer.NpcRoleConfig npcRoleConfig { get; private set; }
    private SyncNinWithGameCreator _syncNinWithGameCreator;
    // private CharacterMovement _characterMovement;
    private NavMeshAgent _navMeshAgent;
    // private bool _isOnWater = false;
    // public MainServer.LocAreaType locAreaType = MainServer.LocAreaType.None; 
    private MapCellTileData _lastTileInfo;
    private uint _flag = 0;
    private uint _flagOther = 0;
    public MapCellTileData lastTileInfo {
        get {
            return _lastTileInfo;
        }
        set {
            _lastTileInfo = value;
            //添加脚步声，还有特效
            if(trainer != null) {
                ninCharacterFootsteps.SetFootstepType(value.tileType);
                if(isMe) {
                    switch (value.tileType)
                    {
                        case TileType.Grass:
                            AudioCenterMgr.PlayMapAudio(AudioCenterMgr.MapType.WalkGrass);
                            break;
                        case TileType.Water:
                            // AudioCenterMgr.PlayMapAudio(AudioCenterMgr.MapType.Jump);
                            break;
                        case TileType.Walkable:
                            break;
                        case TileType.NotWalkable:
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }
    // public void SetSpeedType(NinSpeedType value) {
    //     // _speedType = NinSpeedType.Normal;
    //     _speedType = value;
    //     if(_characterRun != null) {
    //         if(_speedType == NinSpeedType.Fast) {
    //             _characterRun.RunStart();
    //         } else {
    //             _characterRun.RunStop();
    //         }
    //         _characterRun.RunSpeed = (float)_speedType;
    //         // _characterMovement.MovementSpeed
    //     }
    //     if(gameObject) {
    //         var agent = this.gameObject.GetComponent<NavMeshAgent>();

    //         if(agent != null && agent.isActiveAndEnabled) {
    //             agent.speed = (float)_speedType;
    //         }
    //     }
    //     // smartNavFollower.SetMoveSpeed((float)_speedType);
    //     MapController.Current.mapCharacterMgr.SyncFollowCharacter(trainer);
    // }
    // public NinSpeedType speedType {
    //     get {
    //         return _speedType;
    //     }
    //     set {
    //         _speedType = value;
    //         if(_characterRun != null) {
    //             if(_speedType == NinSpeedType.Fast) {
    //                 _characterRun.RunStart();
    //             } else {
    //                 _characterRun.RunStop();
    //             }
    //             _characterRun.RunSpeed = (float)_speedType;
    //             // _characterMovement.MovementSpeed
    //         }
    //         if(gameObject) {
    //             var agent = this.gameObject.GetComponent<NavMeshAgent>();

    //             if(agent != null && agent.isActiveAndEnabled) {
    //                 agent.speed = (float)_speedType;
    //             }
    //         }
    //         // smartNavFollower.SetMoveSpeed((float)_speedType);
    //         MapController.Current.mapCharacterMgr.SyncFollowCharacter(trainer);
    //         // MapCharacterMgr.
    //     }
    // }
    public bool isMe {
        get  {
            return trainer != null && trainer.Id == GameContext.Current.Trainer.Id;
        }
    }
    private bool _canTransfer = false;
    public bool CanTransfer {
        get {
            return this.TECharacter.CharacterType == Character.CharacterTypes.Player && _canTransfer;
        }
        set {
            _canTransfer = value;
        }
    }
    // public void SetFollowCharacter(Transform transform)
    // {
    //     smartNavFollower.SetLeader(transform, 0);
    //     // set
    //     // {
    //     //     _followCharacter = value;
    //     //     // if (ninCharacterPathfindToMouse3D != null)
    //     //     // {
    //     //     //     ninCharacterPathfindToMouse3D.FollowObject = _followCharacter;
    //     //     //     // ninCharacterPathfindToMouse3D.SetMoveSpeed(_followCharacter.speedType)
    //     //     // }
    //     // }
    //     // get
    //     // {
    //     //     return _followCharacter;
    //     // }
    // }
    // private MainServer.TrainerLocInfo _locInfo = new();
    public MainServer.TrainerLocInfo TestLocInfo = new();
    public MainServer.TrainerLoc GetTrainerLoc() {
        MainServer.TrainerLoc aoiUploadLoc = new MainServer.TrainerLoc {
            X = transform.position.x,
            Y = transform.position.y,
            Z = transform.position.z,
            MainLandType = MapController.Current.mapLoader.GetMainLandType(),
            Status = locStatus,
            IsRun = isRun,
            IsSurf = isSurf,
            Flag = _flag,
            // TsMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        return aoiUploadLoc;
    }
    public async UniTask UpdateLocAndMoveIfNeed(MainServer.TrainerLoc loc) {
        if(smartNavFollower.leaderObject != null && trainer != null && !NinGroupMgr.share.IsPartyMember(trainer)) {
            smartNavFollower.ClearLeaderIfNeed();
        }
        if(loc == null) {
            return;
        }
        if(loc.IsRun != isRun) {
            isRun = loc.IsRun;
        }
        if(loc.IsSurf != isSurf) {
            isSurf = loc.IsSurf;
        }
        if(smartNavFollower.leaderObject != null) {
            return;
        }
        await smartNavFollower.SetLoc(loc);
    }
    public void ChangeFlag() {
        _flag ++;
    }
    public void ChangeFlagOther() {
        _flagOther ++;
    }
    // public MainServer.TrainerLocInfo ClearLocInfo() {
    //     if(_locInfo.LocLine.Count > 3) {
    //         TestLocInfo = _locInfo;
    //     }
    //     var locInfo = _locInfo;
    //     locInfo.Speed = GetMoveSpeed();
    //     //获取navagation的speed
    //     // var agent = this.gameObject.GetComponent<NavMeshAgent>();
    //     // if(agent != null) {
    //     //     locInfo.Speed = agent.speed;
    //     // }
    //     float maxCount = 5f;
    //     int totalCount = locInfo.LocLine.Count;

    //     if (totalCount > maxCount)
    //     {
    //         List<MainServer.TrainerLoc> selected = new List<MainServer.TrainerLoc>(); // 用实际类型替换 YourType

    //         for (int i = 0; i < maxCount; i++)
    //         {
    //             int index = (int)((i + 1) * totalCount / (maxCount + 1));
    //             index = Mathf.Clamp(index, 0, totalCount - 1);
    //             selected.Add(locInfo.LocLine[index]);
    //         }
    //         locInfo.LocLine.Clear();
    //         locInfo.LocLine.AddRange(selected);
    //     }
    //     // float maxCount = 5f;
    //     // if(locInfo.LocLine.Count > maxCount) {
    //     //     int gapCount = (int)(locInfo.LocLine.Count/maxCount);
    //     //     int lineCount = locInfo.LocLine.Count;
    //     //     for (int i = 0; i < lineCount; i++)
    //     //     {
    //     //         locInfo.LocLine.RemoveAt(0);
    //     //     }

    //     // }
    //     // PLog.Error("_locInfo.ToByteString().Length: " + _locInfo.ToByteArray().Length);
    //     _locInfo = new();
    //     return locInfo;
    // }
    public Transform GetFollowTransform() {
        return this.transform;
    }
    public MapCellTileData? GetTileInfo() {
        return lastTileInfo;
    }
    // public bool GetIsOnWater() {
    //     if(lastTileInfo == null) {
    //         return false;
    //     }
    //     return lastTileInfo.tileType == TileType.Water;
    //     // return locAreaType == MainServer.LocAreaType.Water;
    // }
    public bool IsMoving() {
        return isMoving;
    }
    // public MainServer.LocAreaType GetLocAreaType() {
    //     if(lastTileInfo == null) {
    //         return MainServer.LocAreaType.None;
    //     }
    //     return ;
    // }
    // public void UpdatePathfindTargetLocInfo(MainServer.TrainerLocInfo locInfo) {

    // }
    public bool isMoving
    {
        get
        {
            if(ninCharacterType == NinCharacterType.Poke) {
                return true;
            }
            if(smartNavFollower.IsMoving) {
                return true;
            }
            // if(ninCharacterPathfindToMouse3D != null && ninCharacterPathfindToMouse3D.LerpingDirection() != Vector3.zero) {
            //     return true;
            // }
            // var moveStatus = gameCreatorCharacter.LinkedInputManager.GetMoveStatus();
            // if (ninCharacterPathfindToMouse3D != null &&
            // ninCharacterPathfindToMouse3D.Target != null &&
            // gameCreatorCharacter.FindAbility<CharacterMovement>().ScriptDrivenInput == false)
            // {
            //     moveStatus = moveStatus || ninCharacterPathfindToMouse3D.NextWaypointIndex >= 0;
            // }
            // return moveStatus;
            // gameCreatorCharacter.LinkedInputManager.GetMoveStatus() || gameCreatorCharacter.
            if (_characterMovement == null) {
                return false;
            }
            var moveEnabled = (_characterMovement.enabled && _characterMovement.ScriptDrivenInput) || (_navMeshAgent.enabled && _navMeshAgent.enabled);
            //更顺滑
            return moveEnabled && (_character.MovementState.CurrentState == CharacterStates.MovementStates.Walking || _character.MovementState.CurrentState == CharacterStates.MovementStates.Running);
            // return false;
            // float MOVE_THRESHOLD = 1.0f;
            // return gameCreatorCharacter.Driver.WorldMoveDirection.magnitude > 0.2f;
        }
    }
    private MainServer.TrainerActionType _status = MainServer.TrainerActionType.Idle;
    public MainServer.TrainerActionType Status
    {
        get
        {
            return _status;
        }
        set
        {
            _status = value;
        }
    }
    // void Start()
    // {
    //     TrainerResourceInfo info = new();
    //     // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
    //     //     trainer = GameContext.Current.Trainer;
    //     // }

    //     // var trainer = GameContext.Current.Trainer;

    //     info.Name = "01";
    //     info.Gender = "f";
    //     animator.SpriteResourceInfo = info;
    // }
    // protected override void Initialization()
    // {
    //     base.Initialization();
    //     ninCharacterFootsteps.SetIsRun(false);
    //     // if(animator.SpriteResourceInfo == null) {
    //     //     TrainerResourceInfo info = new();
    //     //     // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
    //     //     //     trainer = GameContext.Current.Trainer;
    //     //     // }

    //     //     // var trainer = GameContext.Current.Trainer;
    //     //     if(trainer == null && IsLocalTest) {
    //     //         info.Name = "01";
    //     //         info.Gender = "m";
    //     //     } else {
    //     //         info.Name = trainer.Cloth.Nbody;
    //     //         info.Gender = trainer.Gender;
    //     //     }
    //     //     animator.SpriteResourceInfo = info;
    //     // }
    //     // _characterMovement = this.gameObject.GetComponent<CharacterMovement>();
    //     _navMeshAgent = this.gameObject.GetComponent<NavMeshAgent>();
    //     _navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance;
    //     _characterRun = _character.FindAbility<CharacterRun>();
    //     _movement.OnStateChange = () => {
    //         if(_movement.CurrentState == CharacterStates.MovementStates.Running) {
    //             locStatus = MainServer.TrainerLocStatus.LocStatusRun;
    //             // ninCharacterFootsteps.SetIsRun(true);
    //         } else {
    //             locStatus = MainServer.TrainerLocStatus.LocStatusNone;
    //             // ninCharacterFootsteps.SetIsRun(false);
    //         }
    //     };
    // }
    void OnDestroy()
    {
        dialogueZone.CloseDialogue();
    }
    protected override void Initialization()
    {
        base.Initialization();
        _syncNinWithGameCreator = this.gameObject.GetComponent<SyncNinWithGameCreator>();
        _navMeshAgent = this.gameObject.GetComponent<NavMeshAgent>();
        _navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance;
        _characterRun = _character.FindAbility<CharacterRun>();
        ninCharacterFootsteps.SetIsRun(false);
        _movement.OnStateChange = () => {
            if(_movement.CurrentState == CharacterStates.MovementStates.Running) {
                isRun = true;
                // locStatus = MainServer.TrainerLocStatus.LocStatusRun;
                // ninCharacterFootsteps.SetIsRun(true);
            } else {
                isRun = false;
                // locStatus = MainServer.TrainerLocStatus.LocStatusNone;
                // ninCharacterFootsteps.SetIsRun(false);
            }
        };
        // Tast().Forget();
        // StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.AllInventory(), (result) =>
        // {
        //     if (result.Success)
        //     {
        //         var jsonResult = JsonFormatter.Default.Format(result.Content);
        //         DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
        //     }
        // }));
    }
    // public async UniTask Tast() {
    //     await UniTask.Delay(1000);
    //     speedType = NinSpeedType.Fast;
    // }
    // private async UniTaskVoid AllInventory()  {
    //     var result = await Client.Share.AllInventory();
    //     if (result.Success) {
    //         var jsonResult = JsonFormatter.Default.Format(result.Content);
    //         DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
    //     }
    // }
    public Vector3 GetCubeFowardPosition() {
        Vector3 offsetDir = Role.DirectionHelper.GetOffsetFromDirection(GetCurrentDirection());
        float distance = 0.6f;
        Vector3 checkPos = ninCharacterCheckCube.transform.position + offsetDir * distance;
        return checkPos;
    }
    public void OpenCheckCube() {
        ninCharacterCheckCube.gameObject.SetActive(true);
    }
    public bool CheckIsOnWater() {
        var isOnWater = MapController.Current.mapLoader.IsWater(this.transform);
        // if(isOnWater) {
        //     locAreaType = MainServer.LocAreaType.Water;
        // } else if(locAreaType == MainServer.LocAreaType.Water) {
        //     locAreaType = MainServer.LocAreaType.None;
        // }
        return isOnWater;
        // return mapLoader.IsWater(mapCharacterMgr.myCharacter.ninCharacterCheckCube.transform);
    }
    // public bool CheckIsOnLandFaceWater() {
    //     if(locAreaType == MainServer.LocAreaType.Water) {
    //         return false;
    //     }
    //     return MapController.Current.mapLoader.IsWater(this.ninCharacterCheckCube.transform);
    //     // return MapController.Current.mapLoader.IsLand(this.ninCharacterCheckCube.transform);
    // }
    // public bool CheckIsOnWaterFaceLand() {
    //     if(locAreaType == MainServer.LocAreaType.Water) {
    //         return !MapController.Current.mapLoader.IsWater(this.ninCharacterCheckCube.transform);
    //         // return MapController.Current.mapLoader.IsLand(this.ninCharacterCheckCube.transform);
    //     }
    //     return false;
    // }
    public (bool onLand, bool faceWater) CheckIsOnAndFace() {
        var onLand = !CheckIsOnWater();
        if(lastTileInfo == null || lastTileInfo.tileType == TileType.NotWalkable) {
            return (onLand, false);
        }
        var faceWater = CheckIsFaceWater();
        return (onLand, faceWater);
    }
    public bool CheckIsFaceWater() {
        var isOnWater = MapController.Current.mapLoader.IsWater(this.ninCharacterCheckCube.transform);
        return isOnWater;
    }
    public bool IsOnOutdoorEnv() { //检查是否在室外
        return true;
    }
    public bool TryMoveToNavigableWaterCell() {
        var info = MapController.Current.mapLoader.GetTileInfoByTansform(this.ninCharacterCheckCube.transform);
        // info.WordPos.z = info.WordPos.z - 1;
        if(info != null && info.tileType == TileType.Water) {
            AudioCenterMgr.PlayMapAudio(AudioCenterMgr.MapType.Jump);
            MapController.Current.transferMapMgr.MoveToCurrentMap(ninChatacterTransfer, this.ninCharacterCheckCube.transform.position + Vector3.down * 1f);
            // ninChatacterTransfer.Transfer(info.WordPos, (complete) => {

            // });
            return true;
        }
        return false;
    }
    public void MoveToWaterEdgeLand() {
        if(!CheckIsOnWater()) {
            return;
        }
        if (!NavMesh.SamplePosition(this.ninCharacterCheckCube.transform.position + Vector3.up * 0.5f, out NavMeshHit hit, 1f, NavMesh.AllAreas))
        {
            Debug.LogError("❌ 无法在陆地找到可行走的位置");
            return;
        }
        AudioCenterMgr.PlayMapAudio(AudioCenterMgr.MapType.Jump);
        MapController.Current.transferMapMgr.MoveToCurrentMap(ninChatacterTransfer, this.ninCharacterCheckCube.transform.position);
        // ninChatacterTransfer.Transfer(this.ninCharacterCheckCube.transform.position, (complete) => {

        // });
    }
    public void InitNpc(MainServer.NpcRoleConfig config) {
        ninCharacterType = NinCharacterType.Npc;
        npcRoleConfig = config;
        dialogueZone.gameObject.SetActive(true);
        OtherCharacter(config.Name);
        TrainerResourceInfo info = new();
        info.IsNpc = true;
        info.Name = config.Cloth.Name;
        info.Gender = config.Gender;
        animator.SpriteResourceInfo = info;
        ninCharacterTopUI.gameObject.SetActive(false);
        // ninCharacterTopUI.SetNpc(config);

        // dialogueZone.SetSimpleDialogueElements(config.DialogElements.ToArray());
    }
    public void SetTrainer(MainServer.Trainer trainer, bool forceOther = false)
    {
        this.trainer = trainer;
        if(animator.SpriteResourceInfo == null) {
            TrainerResourceInfo info = new();
            // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
            //     trainer = GameContext.Current.Trainer;
            // }

            // var trainer = GameContext.Current.Trainer;

            info.Name = trainer.Cloth.Name;
            info.Gender = trainer.Gender;
            animator.SpriteResourceInfo = info;
        } else {
            UpdateExteriorIfNeed(trainer);
        }
        if(this.TECharacter.PlayerID != this.trainer.Name) {
            var characterController = this.TECharacter.gameObject.GetComponent<CharacterController>();
            if(characterController != null)
            characterController.detectCollisions = false;
            // var rigidbody = this.gameObject.GetComponent<Rigidbody>();
            // rigidbody.detectCollisions = false;
            ninCharacterType = NinCharacterType.Player;
            dialogueZone.gameObject.SetActive(false);

            ninCharacterTopUI.SetTrainer(trainer);
            if (GameContext.Current.Trainer != null)
            {
                if (forceOther || (this.trainer.Id != GameContext.Current.Trainer.Id && this.TECharacter))
                {
                    OtherCharacter(this.trainer.Name);
                }
            }
        }
    }
    public void UpdateExteriorIfNeed(MainServer.Trainer trainer) {
        if(this.trainer == null || this.trainer.Id != trainer.Id) {
            return;
        }
        if(this.trainer.Cloth.Name != trainer.Cloth.Name) {
            TrainerResourceInfo info = new();
            info.Name = trainer.Cloth.Name;
            info.Gender = trainer.Gender;
            animator.SpriteResourceInfo = info;
        }

    }
    public void OpenControlCharacter() {
        // if (_environmentChecker == null)
        // {
        //     _environmentChecker = this.gameObject.AddComponent<NinEnvironmentChecker>();
        // }
        _canTransfer = true;
        OpenCheckCube();
    }
    // public void OpenEnvironmentChecker()
    // {
    // }
    private void OtherCharacter(string name)
    {
        var movement = this.TECharacter.FindAbility<CharacterMovement>();
        movement.Deceleration = 100;
        movement.enabled = false;
        this.TECharacter.PlayerID = name;
        this.characterName = name;
        this.TECharacter.CharacterType = Character.CharacterTypes.AI;
        this.TECharacter.SetInputManager();
        var characterController = this.TECharacter.gameObject.GetComponent<CharacterController>();
        // characterController.detectCollisions = false;
        // characterController.radius = 0.1f;
        // var center = characterController.center;
        // center.y = -90;
        // characterController.center = center;
        topDownController3D.enabled = false;
        characterController.enabled = false;
        topDownController3D.FreeMovement = false;
        // var rigidbody = this.gameObject.GetComponent<Rigidbody>();
        // rigidbody.detectCollisions = false;
        var characterRun = this.TECharacter.FindAbility<CharacterRun>();
        if(characterRun != null) {
            characterRun.enabled = false;
        }
        // this.TECharacter.enabled = false;
        // ninCharacterPathfindToMouse3D.IgnoreDetectMouse = true;
    }
    public void SetPokeInfo(MainServer.TrainerFollowPokeInfo pokeInfo)
    {
        ninCharacterType = NinCharacterType.Poke;
        dialogueZone.gameObject.SetActive(false);
        ninCharacterTopUI.SetPokeInfo(pokeInfo);
        if (this.TECharacter != null)
        {
            OtherCharacter(pokeInfo.Id.ToString());
        }
        LoadAnimation(pokeInfo);
    }
    void LoadAnimation(MainServer.TrainerFollowPokeInfo pokeInfo) {
        // var localPoke = new NinLocalPokeInfo(pokeInfo.Name);
        // var localPokemonData = await PokeDataLoad.Share.GetIndexData<Pokemon>(pokeInfo.Name);//pokeInfo.LocalPokemonData();
        // if(localPoke != null) {
        // }
        PokemonResourceInfo resourceInfo = new(pokeInfo.Name, pokeInfo.Shiny, pokeInfo.Gender, PokemonResourceConditions.Followers);
        this.animator.autoRefreshFollowPokeContentSize = true;
        this.animator.infoLoadComplete = (size) => {
            var originScaleToWidth = 64;
            smartNavFollower.SetCloseDistance(size.x / originScaleToWidth);
            // ninCharacterPathfindToMouse3D.SetFlowDistance(size.x / originScaleToWidth);
        };
        this.animator.SpriteResourceInfo = resourceInfo;
    }
    // void Awake()
    // {
    //     TrainerResourceInfo info = new();
    //     var trainer = GameContext.Current.Trainer;
    //     if(trainer == null && IsLocalTest) {
    //         info.Name = "01";
    //         info.Gender = "m";
    //     } else {
    //         info.Name = trainer.Cloth.Nbody;
    //         info.Gender = trainer.Gender;
    //     }
    //     animator.SpriteResourceInfo = info;
    // }
    // void Start()
    // {
    //     // NinTopDownController3D.MinimumGroundedDistance = Single.MaxValue;
    //     // gameCreatorCharacter.
    //     // GetComponent<CharacterController>().detectCollisions = false;
    //     StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.AllInventory(), (result) => {
    //             if(result.Success) {
    //                 var jsonResult = JsonFormatter.Default.Format(result.Result);
    //                 DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
    //             }
    //     }));
    // }
    public Direction GetCurrentDirection() {
        if(_syncNinWithGameCreator == null) {
            return Role.DirectionHelper.GetFacingDirection(GetMoveDirection()) ?? Direction.N;
        }
        return _syncNinWithGameCreator.currentDirection;
    }
    public void TrySetCurrentDirection(Direction direction) {
        Debug.Log("++++++++++++++++: TrySetCurrentDirection: " + direction);
        animator.ChangeDirectionIfNeed(direction, true);
        if(_syncNinWithGameCreator != null) {
            _syncNinWithGameCreator.ChangeDirection(direction);
        }
    }
    // public void SetLocStatus(MainServer.TrainerLocStatus status) {
    //     _locStatus = status;
    //     // animator.ChangeStatusIfNeed(status);
    // }
    // public MainServer.TrainerLocStatus GetLocStatus() {
    //     return _locStatus;
    // }
    public Vector3 GetMoveDirection() {
        // var v3 = ninCharacterPathfindToMouse3D.LerpingDirection();
        var v3 = smartNavFollower.GetMoveDirection();
        if(v3 != Vector3.zero) {
            return v3;
        }
        return topDownController3D.InputMoveDirection;
    }
    public override void ProcessAbility()
    {
        base.ProcessAbility();
        long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        var time = GameConst.Config.TrainerLocLineTime;
        // if(ninCharacterFootsteps != null) {
        //     ninCharacterFootsteps.SetIsRun(smartNavFollower.IsMoving);
        // }
        // if(_lastUpPosition == this.transform.position) {
        //     time = 300 * 10 * 10; // 30s
        // }
        // if(isMe) {
        //     if(_character != null &&_character.MovementState.CurrentState == CharacterStates.MovementStates.Running) {
        //         // Debug.LogError($"=========上传的速度3 {NinSpeedType.Fast} {_characterMovement.MovementSpeed} {_movement.CurrentState}");
        //         speedType = NinSpeedType.Fast;
        //     } else {
        //         // Debug.LogError($"=========上传的速度3 {NinSpeedType.Normal} {_characterMovement.MovementSpeed} {_movement.CurrentState}");
        //         speedType = NinSpeedType.Normal;
        //     }
        // }
        if(MapController.Current.mapLoader.GetMapInfo() == null) {
            return;
        }
        if (timestamp - _lastLocLineTs > time)
        {
            if(!smartNavFollower.IsMoving && smartNavFollower.leaderObject != null && smartNavFollower.leaderObject.GetFollowTransform() != null) {
                var v3 = smartNavFollower.leaderObject.GetFollowTransform().position - transform.position;
                var dir = Role.DirectionHelper.GetFacingDirection(v3) ?? GetCurrentDirection();
                if(dir != GetCurrentDirection()) {
                    Debug.Log("++++++++++++++++: change direction: " + dir);
                    TrySetCurrentDirection(dir); //没有动画
                }
            }
            // CheckerResult checkerResult = null;
            // if (_environmentChecker != null)
            // {
            //     checkerResult = _environmentChecker.DetectGroundEdge();
            //     if (isMoving)
            //     {
            //         MapController.Current.HandleCheckerResult(this, checkerResult, (success) =>
            //         {
            //             if (!success)
            //             {
            //                 Status = MainServer.TrainerActionType.Idle;
            //             }
            //         });
            //     }
            // }
            var areaType = MainServer.LocAreaType.None;
            if(ninCharacterCheckCube.isActiveAndEnabled) {
                var direction = GetCurrentDirection();
                ninCharacterCheckCube.UpdateCharacter(direction);
                var (onLand, faceWater) = CheckIsOnAndFace();
                List<MapMenuBtns.RightBottomOprationType> btnTypes = new();
                if(faceWater) {
                    btnTypes.Add(MapMenuBtns.RightBottomOprationType.FaceWater);
                }
                if(this.ninChatacterTransfer.onTransmitPoint != null) {
                    if(this.ninChatacterTransfer.IsManualTransfer(true)) {
                        btnTypes.Add(MapMenuBtns.RightBottomOprationType.ManualTransmit);
                    }
                }
                isSurf = !onLand;
                if(onLand) {
                    // animator.ChangeStatusIfNeed(NinCharacterStatus.Normal);
                    btnTypes.Add(MapMenuBtns.RightBottomOprationType.OnLand);
                    // if(CheckIsOnWaterFaceLand()) {
                    //      ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.OnWaterFaceLand);
                    // } else {
                    //     ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.Water);
                    // }
                    // ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.Water);
                    // Debug.LogError($"角色{this.trainer.Name}在水中");
                } else {
                    areaType = MainServer.LocAreaType.Water;
                    // locStatus = 
                    
                    // animator.ChangeStatusIfNeed(NinCharacterStatus.Surf);
                    btnTypes.Add(MapMenuBtns.RightBottomOprationType.OnWater);
                    // if(CheckIsOnLandFaceWater()) {
                    //     ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.OnLandFaceWater);
                    //     // TryMoveToNavigableWaterCell();
                    //     // Debug.LogError($"角色{this.trainer.Name}在水边上，并且面向着水面");
                    // } else {
                    //     ninCharacterTopUI.oprationTip.ConfigType(NinChatacterOprationTip.OprationType.None);
                    // }
                }
                if(MapController.Current != null && MapController.Current.mapMenuUI != null && MapController.Current.mapMenuUI.mapMenuBtns != null) {
                    MapController.Current.mapMenuUI.mapMenuBtns.SetRightBottomOprationType(btnTypes);
                }
            } else if(this.trainer != null){
                if(CheckIsOnWater()) {
                    animator.ChangeStatusIfNeed(NinCharacterStatus.Surf);
                } else {
                    animator.ChangeStatusIfNeed(NinCharacterStatus.Normal);
                }
            }
            if(isMe) { 
                _lastLocLineTs = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                // var loc = new MainServer.TrainerLoc
                // {
                //     // ReginId = checkerResult.tilePrefabComponent.RegionId,
                //     X = transform.position.x,
                //     Y = transform.position.y,
                //     Z = transform.position.z,
                //     Status = locStatus,
                //     // Speed = GetMoveSpeed(),
                //     // AreaType = areaType,
                //     MainLandType = MapController.Current.mapLoader.GetMainLandType(),
                //     // TsMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                //     // MapName = MapController.Current.mapLoader.GetMapInfo().GetMapNameId()
                // };
                // // if(!loc.Equals(_locInfo.LocLine.LastOrDefault())) {
                // //     _locInfo.LocLine.Add(loc);
                // //     Debug.Log($"=========上传的速度1 {loc.Speed}");
                // //     // 限制路径点数量，避免数据包过大
                // //     // if(_locInfo.LocLine.Count > 5) {
                // //     //     _locInfo.LocLine.RemoveAt(0);
                // //     // }
                // // }
                // _locInfo.Loc = loc.Clone();
            }
            
            // new MainServer.TrainerLoc
            // {
            //     // ReginId = checkerResult.tilePrefabComponent.RegionId,
            //     X = transform.position.x,
            //     Y = transform.position.y,
            //     Z = transform.position.z,
            //     Speed = GetMoveSpeed(),
            //     AreaType = areaType,
            //     MainLandType = MapController.Current.mapLoader.GetMainLandType(),
            //     MapName = MapController.Current.mapLoader.GetMapInfo().GetMapNameId()
            // };
            // if(checkerResult != null && checkerResult.tilePrefabComponent != null) {
            //     var loc = new MainServer.TrainerLoc
            //     {
            //         ReginId = checkerResult.tilePrefabComponent.RegionId,
            //         X = transform.position.x,
            //         Y = transform.position.y,
            //         Z = transform.position.z,
            //         AreaType = areaType,
            //         MainLandType = MapController.Current.mapLoader.GetMainLandType(),
            //         MapName = MapController.Current.mapLoader.GetMapInfo().GetMapNameId()
            //     };
            //     if(!loc.Equals(_locInfo.LocLine.LastOrDefault())) {
            //         _locInfo.LocLine.Add(loc);
            //         // 限制路径点数量，避免数据包过大
            //         // if(_locInfo.LocLine.Count > 5) {
            //         //     _locInfo.LocLine.RemoveAt(0);
            //         // }
            //     }
            //     _locInfo.Loc = new MainServer.TrainerLoc
            //     {
            //         ReginId = checkerResult.tilePrefabComponent.RegionId,
            //         X = transform.position.x,
            //         Y = transform.position.y,
            //         Z = transform.position.z,
            //         AreaType = areaType,
            //         MainLandType = MapController.Current.mapLoader.GetMainLandType(),
            //         MapName = MapController.Current.mapLoader.GetMapInfo().GetMapNameId()
            //     };
            // }
        }
        //不能注释，不然topdown的状态一致都不是落地的状态
        // topDownController3D.Grounded = true;
        // _locInfo.Loc = new MainServer.TrainerLoc
        // {
        //     ReginId = "test_map",
        //     X = transform.position.x,
        //     Y = transform.position.y,
        //     Z = transform.position.z
        // };

        // if(pokemon != null) {
        //     PokemonResourceInfo info = new();
        //     info.Conditions = PokemonResourceConditions.Followers;
        //     info.Pokemon = pokemon;
        //     info.Shiny = false;
        //     info.Gender = "";
        //     this.animator.SpriteResourceInfo = info;
        //     pokemon = null;
        // }
        
        // // if(TECharacter != null && TECharacter.PlayerID == "Player1") {
        // //     trainer = GameContext.Current.Trainer;
        // // }
        // if (_environmentChecker != null)
        // {
        //     if (isMoving)
        //     {
        //         var result = _environmentChecker.DetectGroundEdge();
        //         MapController.Current.HandleCheckerResult(this, result, (success) =>
        //         {
        //             if (!success)
        //             {
        //                 Status = MainServer.TrainerActionType.Idle;
        //             }
        //         });
        //     }
        // }

        // if(_character.CharacterType == Character.CharacterTypes.Player && trainer != null && Status != MainServer.TrainerActionType.Battle) {
        //     // if(_environmentChecker != null) {
        //     //     if(isMoving && _environmentChecker.DetectGroundEdge()) {
        //     //         MapController.current.EnterWildBattle();
        //     //     }
        //     // }
        //     UpdateLoc();
        // }
    }
    // public void StopMove() {
    //     _character.FindAbility<CharacterMovement>().ScriptDrivenInput = false;
    //     // _character.MovementState.ChangeState(CharacterStates.MovementStates.Idle);
    //     // _character.Freeze();
    //     // _character.UnFreeze();
    // }
    public void Freeze() {
        _character.Freeze();
    }
    public void UnFreeze() {
        _character.UnFreeze();
    }
    // public MainServer.TrainerLoc GetTrainerLoc() {
    //     return new MainServer.TrainerLoc
    //     {
    //         X = transform.position.x,
    //         Y = transform.position.y,
    //         Z = transform.position.z,
    //         Status = locStatus,
    //         // AreaType = locAreaType,
    //         MainLandType = MapController.Current.mapLoader.GetMainLandType(),
    //         // MapName = MapController.Current.mapLoader.GetMapInfo().GetMapNameId()
    //     };
    // }
    public void SetBaseMoveSpeed(int speed) {
        _baseMoveSpeed = speed;
        if(_characterMovement != null) {
            _characterMovement.WalkSpeed = _baseMoveSpeed;
            _characterMovement.ResetAbility();
        }
    }
    public int GetBaseMoveSpeed() {
        return _baseMoveSpeed;
    }
    public bool IsRide() {
        return trainer.FollowPoke.Ride != MainServer.TrainerRideType.RideNone;
    }
    public float GetMoveSpeed()
    {
        if(_characterMovement == null) {
            return NinSpeed.walk;
        }
        if(isRun) {
            return IsRide() ? NinSpeed.ridRun : NinSpeed.run;
        } else {
            return IsRide() ? NinSpeed.ridWalk : NinSpeed.walk;
        }
        // return NinSpeed.walk;
        // return _characterMovement.MovementSpeed;
        // if(_character != null &&_character.MovementState.CurrentState == CharacterStates.MovementStates.Running) {
        //     // Debug.LogError($"=========上传的速度3 {NinSpeedType.Fast} {_characterMovement.MovementSpeed} {_movement.CurrentState}");
        //     // speedType = NinSpeedType.Fast;
        //     return _characterMovement.MovementSpeed;
        // } else {
        //     // Debug.LogError($"=========上传的速度3 {NinSpeedType.Normal} {_characterMovement.MovementSpeed} {_movement.CurrentState}");
        //     speedType = NinSpeedType.Normal;
        // }
        // return (float)speedType;
        // if(isMe) {
        //     if(_character != null &&_character.MovementState.CurrentState == CharacterStates.MovementStates.Running) {
        //         // Debug.LogError($"=========上传的速度3 {NinSpeedType.Fast} {_characterMovement.MovementSpeed} {_movement.CurrentState}");
        //         speedType = NinSpeedType.Fast;
        //     } else {
        //         // Debug.LogError($"=========上传的速度3 {NinSpeedType.Normal} {_characterMovement.MovementSpeed} {_movement.CurrentState}");
        //         speedType = NinSpeedType.Normal;
        //     }
        // }
    }
    // void Update()
    // {
    //     NinTopDownController3D.Grounded = true;
    //     if(_character.CharacterType == Character.CharacterTypes.Player && trainer != null && Status != MainServer.TrainerActionType.Battle) { 
    //         UpdateLoc();
    //     }
    // }

    // void UpdateLoc() {
    //     long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
    //     var time = 300;
    //     if(_lastUpPosition == this.transform.position) {
    //         time = 300 * 10 * 10; // 30s
    //     }
    //     if(timestamp - lastLocTs > time) {
    //         Debug.Log($"=========:1 {this.transform.position}");
    //         StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.UpdateUserLoc("test_map", this.transform.position.x, this.transform.position.y, this.transform.position.z), (result) => {

    //             if(result.Success) {
    //                 _lastUpPosition = this.transform.position;
    //                 // Debug.Log($"=========:2 {result.Result.Trainers.FirstOrDefault().Loc}");
    //                 MapController.Current.UpdateOtherTrainers(result.Result.Trainers.ToArray());
    //             }
    //         }));
    //         lastLocTs = timestamp;
    //     }
    //     if(lastLocTs == 0) {
    //         lastLocTs = timestamp;
    //     }
    // }

}