using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

public class NinSmartNavFollower : MonoBehaviour
{
    [Header("核心组件")]
    public NinSmartNavMover smartNavMover;

    // [Header("跟随配置")]
    public INinCharacterFollowObject leaderObject { get; private set; }          // 主角
    private NinChatacterTransfer _ninChatacterTransfer;
    private bool _isPoke = true;
    private int slotIndex = 0;          // 插槽编号（0=最靠近）
    private float slotSpacing = 1f;   // 插槽间距
    private float updateRate = 0.1f;    // 更新频率（提高到0.1秒）
    private float closeDistance = 1f; // 停止移动判定（增大到1f）
    private float directionUpdateThreshold = 1.5f; // 主角走这么远才更新方向（减小到1.0f）
    private float interpolationSpeed = 8f;         // 插值平滑速度（提高到8f）
    private MainServer.TrainerLocInfo _currentLocInfo;

    // [Header("调试")]
    private bool showDebugGizmos = true;

    private Coroutine followRoutine;
    private Vector3 lastLeaderPos;
    private Vector3 lastLeaderDir; // 缓存的主角行进方向
    private Vector3 targetPos;     // 当前目标点
    private Vector3 lastTargetPos; // 上一次的目标点，用于避免频繁更新
    private float targetUpdateThreshold = 0.8f; // 目标点更新阈值
    private MainServer.TrainerLoc _lastLoc;
    private long _lastSetLocTs = 0;

    public bool IsMoving => smartNavMover.IsMoving;
    public Vector3 GetMoveDirection()
    {
        var dir = smartNavMover.GetMoveDirection();
        return dir;
    }
    void Awake()
    {
        _ninChatacterTransfer = this.gameObject.GetComponentInChildren<NinChatacterTransfer>();
    }
    // public bool IsMoving
    // {
    //     get
    //     {
    //         return followRoutine != null;
    //     }
    // }
    public void SetCloseDistance(float distance)
    {
        closeDistance = 1;
        // closeDistance = distance;
    }
    public bool TransferIfNeed(MainServer.TrainerLoc loc)
    {
        if (_lastLoc != null && (_lastLoc.MainLandType != loc.MainLandType
            || ((loc.IsSurf || _lastLoc.IsSurf) && loc.IsSurf != _lastLoc.IsSurf) || 
            System.Math.Abs(loc.Y - _lastLoc.Y) > 2f ))
        {
            if (loc.MainLandType != _lastLoc.MainLandType)
            {
                MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(loc.MainLandType), new Vector3(loc.X, loc.Y, loc.Z)).Forget();
            }
            else
            {
                MapController.Current.transferMapMgr.MoveToCurrentMap(_ninChatacterTransfer, new Vector3(loc.X, loc.Y, loc.Z));
            }
            _lastLoc = loc;
            return true;
        }
        return false;
    }
    public async UniTask SetLoc(MainServer.TrainerLoc loc)
    {
        long now = System.DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        Vector3 newPos = new Vector3(loc.X, loc.Y, loc.Z);

        float calculatedSpeed = 0f;

        if (_lastLoc != null && _lastSetLocTs > 0)
        {
            Vector3 lastPos = new Vector3(transform.position.x, transform.position.y, transform.position.z);
            // Vector3 serverVelocity = (newPos - lastPos) / loc.ServerLoopTs;
            float dist = Vector3.Distance(newPos, lastPos);
            float dt = loc.ServerLoopTs / 1000f;
            // float dt = (now - _lastSetLocTs) / 1000f;   // 毫秒 → 秒

            if (dt > 0.0001f && dist > 0.001f)
            {
                calculatedSpeed = dist / dt;  // m/s
            }
        }

        _lastLoc = loc;
        _lastSetLocTs = now;

        // 如果服务器自己带了速度，就用服务器速度
        // float serverMoveSpeed = _ninChatacterTransfer.ninCharacter.GetMoveSpeed();

        // // 使用你希望的 final speed（任选一种）
        // float finalMoveSpeed = serverMoveSpeed > 0 ? serverMoveSpeed : calculatedSpeed;

        var moveInfo = new NinSmartNavMover.MoveInfo()
        {
            position = newPos,
            moveSpeed = calculatedSpeed,
            trainerLoc = loc
        };
        if(moveInfo.moveSpeed < 1) {
            return;
        }
        Debug.Log("++++++++++++++++: 移动到目标位置: " + moveInfo.position + " 距离：" + Vector3.Distance(transform.position, moveInfo.position) + " 速度：" + moveInfo.moveSpeed);
        smartNavMover.MoveToPosition(moveInfo, false);
    }
    // public async UniTask SetLoc(MainServer.TrainerLoc loc) {
    //     long ts = System.DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
    //     if(_lastLoc != null) {
    //         if(_lastLoc.MainLandType == loc.MainLandType && _lastLoc.X == loc.X && _lastLoc.Y == loc.Y && _lastLoc.Z == loc.Z && _lastLoc.Status == loc.Status && _lastLoc.TsMs == loc.TsMs && _lastLoc.Flag == loc.Flag && _lastLoc.FlagOther == loc.FlagOther) {
    //             _lastSetLocTs = ts;
    //             return;
    //         }
    //     }
    //     // if(leaderObject != null && !isLeader) {
    //     //     return;
    //     // }
    //     if(TransferIfNeed(loc)) {
    //         smartNavMover.StopMoving();
    //         _lastSetLocTs = ts;
    //         return;
    //     }
    //     // var position = new Vector3(loc.X, loc.Y, loc.Z);
    //     // float distance = Vector3.Distance(transform.position, position);
    //     // if(distance < )
    //     // if (loc.MainLandType != locInfo.Loc.MainLandType && loc.MapName != locInfo.Loc.MapName && _ninChatacterTransfer != null && _ninChatacterTransfer.ninCharacter.isMe)
    //     // {
    //     //     smartNavMover.StopMoving();
    //     //     await MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(transformLoc.MainLandType), position);
    //     // }
    //     // smartNavMover.StopMoving();
    //     // await MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(transformLoc.MainLandType), position);
    //     var position = new Vector3(loc.X, loc.Y, loc.Z);
    //     // if(_lastLoc != null && _lastSetLocTs != 0) {
    //     //     var lastPosition = new Vector3(_lastLoc.X, _lastLoc.Y, _lastLoc.Z);
    //     //     if(position != lastPosition) {
    //     //         position = PredictNextPosition(lastPosition, position, 0.3f, (ts - _lastSetLocTs)/1000f);
    //     //     }
    //     // }
    //     _lastLoc = loc;
    //     var moveInfo = new NinSmartNavMover.MoveInfo() { position = position, moveSpeed = _ninChatacterTransfer.ninCharacter.GetMoveSpeed(), trainerLoc = loc };
    //     // Debug.Log("++++++++++++++++: 移动到目标位置: " + moveInfo.position + " 距离：" + Vector3.Distance(transform.position, moveInfo.position) + " 速度：" + moveInfo.moveSpeed);
    //     smartNavMover.MoveToPosition(moveInfo, false);
    // }
    // public async UniTask MoveToNewPostion(MainServer.MainLandType mainLandType, Vector3 position) {
    //     smartNavMover.StopMoving();
    //     if(_lastLoc == null || mainLandType != _lastLoc.MainLandType) {
    //         await MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(mainLandType), position);
    //     } else {
    //         await _ninChatacterTransfer.Transfer(position);
    //     }

    // }
    // public async UniTask SetLocInfo(MainServer.TrainerLocInfo locInfo)
    // {
    //     //链路地址
    //     if (locInfo == null)
    //     {
    //         return;
    //     }
    //     if(_currentLocInfo != null && _currentLocInfo.LastUpdateTs == locInfo.LastUpdateTs) {
    //         return;
    //     }
    //     _currentLocInfo = locInfo;
    //     var locs = new List<NinSmartNavMover.MoveInfo>();
    //     MainServer.TrainerLoc? transformLoc = null;
    //     // foreach (var loc in locInfo.LocLine)
    //     // {
    //     //     if (loc.MainLandType != locInfo.Loc.MainLandType && loc.MapName != locInfo.Loc.MapName && _ninChatacterTransfer != null && _ninChatacterTransfer.ninCharacter.isMe)
    //     //     {
    //     //         transformLoc = loc;
    //     //         locs.Clear();
    //     //     }
    //     //     locs.Add(new NinSmartNavMover.MoveInfo() { position = new Vector3(loc.X, loc.Y, loc.Z), moveSpeed = loc.Speed, trainerLoc = loc });
    //     // }
    //     //其他角色可以不用考虑加载新地图，直接传送就行
    //     if(transformLoc != null && _ninChatacterTransfer != null && _ninChatacterTransfer.ninCharacter.isMe) {
    //         var position = new Vector3(transformLoc.X, transformLoc.Y, transformLoc.Z);
    //         smartNavMover.StopMoving();
    //         await MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(transformLoc.MainLandType), position);
    //     }
    //     if (locs.Count == 0)
    //     {
    //         locs.Add(new NinSmartNavMover.MoveInfo() { position = new Vector3(locInfo.Loc.X, locInfo.Loc.Y, locInfo.Loc.Z), moveSpeed = GetSpeedByStatus(locInfo.Loc.Status), trainerLoc = locInfo.Loc });
    //     }
    //     smartNavMover.SetNewTargetLocs(locs.ToArray());
    // }
    // public float GetSpeedByStatus(MainServer.TrainerLocStatus status) {
    //     return 3.0f;
    // }

    public void ResetPath()
    {
        // StopFollow();
        smartNavMover.StopMoving();
    }

    void OnEnable()
    {
        if (leaderObject != null)
        {
            StartFollow();
        }
    }
    public void ClearLeaderIfNeed()
    {
        if (leaderObject == null)
        {
            return;
        }
        leaderObject = null;
        StopFollow();
    }
    public void SetLeader(INinCharacterFollowObject newLeader, int newSlotIndex)
    {
        if (newLeader == leaderObject && newSlotIndex == slotIndex)
        {
            return;
        }
        if (newLeader == null)
        {
            leaderObject = null;
            StopFollow();
            return;
        }
        leaderObject = newLeader;
        slotIndex = newSlotIndex;
        StartFollow();
    }

    // public void SetMoveSpeed(float speed)
    // {
    //     smartNavMover.SetMoveSpeed(speed);
    // }

    public void StartFollow()
    {
        StopFollow();
        _ninChatacterTransfer.ninCharacter.Freeze();
        followRoutine = StartCoroutine(FollowLeader(true));
    }

    public void StopFollow()
    {
        if (followRoutine != null)
        {
            StopCoroutine(followRoutine);
            followRoutine = null;
        }
        smartNavMover.StopMoving();
        _ninChatacterTransfer.ninCharacter.UnFreeze();
    }

    private IEnumerator FollowLeader(bool first = false)
    {
        if (leaderObject == null || leaderObject.GetFollowTransform() == null)
        {
            yield break;
        }
        var leader = leaderObject.GetFollowTransform();
        lastLeaderPos = leader.position;
        lastLeaderDir = leader.forward;

        while (true && leaderObject != null)
        {
            if (leader != null)
            {

                _ninChatacterTransfer.ninCharacter.Freeze();
                _isPoke = _ninChatacterTransfer.ninCharacter.ninCharacterType == NinCharacter.NinCharacterType.Poke;
                if (!_isPoke)
                {
                    if (TransferIfNeed(leaderObject.GetTrainerLoc()))
                    {
                        yield return new WaitForSeconds(updateRate);
                    }
                    // yield return new WaitForSeconds(updateRate);
                    // var locInfo = leaderObject.GetTrainerLoc();
                    // var loc = _ninChatacterTransfer.ninCharacter.GetTrainerLoc();
                    // if (loc.MainLandType != locInfo.MainLandType && _ninChatacterTransfer != null && _ninChatacterTransfer.ninCharacter.isMe)
                    // {
                    //     var position = new Vector3(locInfo.X, locInfo.Y, locInfo.Z);
                    //     smartNavMover.StopMoving();
                    //     MapController.Current.transferMapMgr.MoveToNewMap(_ninChatacterTransfer, MainMapInfo.Create(locInfo.MainLandType), position).Forget();
                    //     yield return new WaitForSeconds(updateRate);
                    // }
                }
                // 检查 leader 是否移动了足够距离
                // float leaderMoved = Vector3.Distance(leader.position, lastLeaderPos);
                // if (leaderMoved > directionUpdateThreshold)
                // {
                //     // 根据主角移动方向更新阵型方向
                Vector3 moveDir = (leader.position - lastLeaderPos).normalized;
                if (moveDir.sqrMagnitude > 0.01f)
                {
                    lastLeaderDir = moveDir;
                }
                lastLeaderPos = leader.position;
                // }
                float leaderDis = Vector3.Distance(leader.position, transform.position);
                if (leaderDis < closeDistance && !first)
                {
                    yield return new WaitForSeconds(updateRate);
                    continue;
                }

                // 计算插槽位置
                Vector3 leaderRight = Vector3.Cross(Vector3.up, lastLeaderDir);
                int rowCount = _isPoke ? 2 : 3;
                int row = (slotIndex / rowCount) + 1;
                int side = (slotIndex % rowCount == 0) ? -1 : 1;
                if (!_isPoke)
                {
                    switch (slotIndex % rowCount)
                    {
                        case 0:
                            side = -1;
                            break;
                        case 1:
                            side = 0;
                            break;
                        case 2:
                            side = 1;
                            break;
                    }
                }
                var finalSlotSpacing = _isPoke ? slotSpacing * 0.7f : slotSpacing * 1.2f;
                // int side = (slotIndex % rowCount == 0) ? -1 : 1; // 左右交替
                Vector3 offset = (lastLeaderDir * -finalSlotSpacing * row) + (leaderRight * side * finalSlotSpacing);

                // 目标位置（平滑插值）
                Vector3 desiredPos = leader.position + offset;
                // targetPos = targetPos == Vector3.zero ? transform.position : targetPos;
                targetPos = desiredPos;
                // targetPos = Vector3.Lerp(targetPos == Vector3.zero ? transform.position : targetPos, desiredPos, Time.deltaTime * interpolationSpeed);

                // 距离检查 - 只有在目标位置变化足够大时才更新移动
                float distanceToTarget = Vector3.Distance(transform.position, targetPos);
                // float targetPosChange = Vector3.Distance(targetPos, lastTargetPos);
                // Debug.Log("++++++++++++++++: 目标位置: " + targetPos + " 距离：" + leaderDis + " finalSlotSpacing: " + finalSlotSpacing);
                if (distanceToTarget > closeDistance || first)
                {
                    // 只有当目标位置变化超过阈值时才重新设置移动目标
                    if (leaderDis > targetUpdateThreshold || first) // || !smartNavMover.IsMoving
                    {
                        first = false;
                        // Debug.Log("++++++++++++++++: 移动到目标位置: " + targetPos + " 距离：" + leaderDis + " 速度：" + leaderObject.GetMoveSpeed());
                        var trainerLoc = leaderObject.GetTrainerLoc();
                        var moveInfo = new NinSmartNavMover.MoveInfo() { position = targetPos, moveSpeed = leaderObject.GetMoveSpeed(), trainerLoc = trainerLoc };
                        smartNavMover.MoveToPosition(moveInfo, true);
                        lastTargetPos = targetPos;
                    }
                }
                else if (smartNavMover.IsMoving && distanceToTarget <= closeDistance)
                {
                    Debug.Log($"++++++++++++++++: 停止移动 distanceToTarget: {distanceToTarget}, closeDistance: {closeDistance}");
                    smartNavMover.StopMoving();
                }
            }
            first = false;
            yield return new WaitForSeconds(updateRate);
        }
    }

    private void OnDrawGizmos()
    {
        if (showDebugGizmos && leaderObject != null && leaderObject.GetFollowTransform() != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawSphere(targetPos, 0.2f);
            Gizmos.DrawLine(transform.position, targetPos);
        }
    }

    /// <summary>
    /// 根据服务器上次与当前同步的位置，预测玩家下一帧可能的位置
    /// </summary>
    /// <param name="prevPos">上次服务器同步位置</param>
    /// <param name="currPos">当前服务器同步位置</param>
    /// <param name="syncInterval">服务器同步间隔（秒）</param>
    /// <param name="timeSinceLastSync">当前距离上次同步经过的时间（秒）</param>
    /// <returns>预测的下一位置</returns>
    public static Vector3 PredictNextPosition(Vector3 prevPos, Vector3 currPos, float syncInterval, float timeSinceLastSync)
    {
        // 计算速度向量
        Vector3 velocity = (currPos - prevPos) / syncInterval;

        // 预测下一帧的位置（外推）
        Vector3 predictedPos = currPos + velocity * timeSinceLastSync;

        return predictedPos;
    }
}