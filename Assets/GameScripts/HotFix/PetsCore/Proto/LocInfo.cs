// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: MainServer/LocInfo.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace MainServer {

  /// <summary>Holder for reflection information generated from MainServer/LocInfo.proto</summary>
  public static partial class LocInfoReflection {

    #region Descriptor
    /// <summary>File descriptor for MainServer/LocInfo.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static LocInfoReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChhNYWluU2VydmVyL0xvY0luZm8ucHJvdG8SCk1haW5TZXJ2ZXIinAIKClRy",
            "YWluZXJMb2MSCQoBeBgBIAEoAhIJCgF5GAIgASgCEgkKAXoYAyABKAISMAoO",
            "bWFpbl9sYW5kX3R5cGUYBCABKA4yGC5NYWluU2VydmVyLk1haW5MYW5kVHlw",
            "ZRIPCgdwY19uYW1lGAUgASgJEiwKBnN0YXR1cxgGIAEoDjIcLk1haW5TZXJ2",
            "ZXIuVHJhaW5lckxvY1N0YXR1cxIOCgZpc19ydW4YByABKAgSDwoHaXNfc3Vy",
            "ZhgIIAEoCBINCgV0c19tcxgJIAEoAxIMCgRmbGFnGAogASgNEhIKCnRyYWlu",
            "ZXJfaWQYCyABKAMSEgoKZmxhZ19vdGhlchgMIAEoDRIWCg5zZXJ2ZXJfbG9v",
            "cF90cxgNIAEoDSJpCgxBT0lCcm9hZGNhc3QSMAoObWFpbl9sYW5kX3R5cGUY",
            "ASABKA4yGC5NYWluU2VydmVyLk1haW5MYW5kVHlwZRInCgdwbGF5ZXJzGAMg",
            "AygLMhYuTWFpblNlcnZlci5UcmFpbmVyTG9jIqoBChlFbmNvdW50ZXJNZXRo",
            "b2RQb2tlRGV0YWlsEhEKCW1pbl9sZXZlbBgBIAEoBRIRCgltYXhfbGV2ZWwY",
            "AiABKAUSEgoKbWF4X2NoYW5jZRgDIAEoBRIVCg1tZXRob2Rfc3RyaW5nGAQg",
            "ASgJEisKBm1ldGhvZBgFIAEoDjIbLk1haW5TZXJ2ZXIuRW5jb3VudGVyTWV0",
            "aG9kEg8KB3ZlcnNpb24YBiABKAkiuQEKE0VuY291bnRlck1ldGhvZERhdGES",
            "OQoFcG9rZXMYASADKAsyKi5NYWluU2VydmVyLkVuY291bnRlck1ldGhvZERh",
            "dGEuUG9rZXNFbnRyeRISCgptYXhfY2hhbmNlGAIgASgFGlMKClBva2VzRW50",
            "cnkSCwoDa2V5GAEgASgJEjQKBXZhbHVlGAIgASgLMiUuTWFpblNlcnZlci5F",
            "bmNvdW50ZXJNZXRob2RQb2tlRGV0YWlsOgI4ASLfAQodUmVnaW9uQXJlYUVu",
            "Y291bnRlck1ldGhvZERhdGESYQoVZW5jb3VudGVyX21ldGhvZF9kYXRhGAEg",
            "AygLMkIuTWFpblNlcnZlci5SZWdpb25BcmVhRW5jb3VudGVyTWV0aG9kRGF0",
            "YS5FbmNvdW50ZXJNZXRob2REYXRhRW50cnkaWwoYRW5jb3VudGVyTWV0aG9k",
            "RGF0YUVudHJ5EgsKA2tleRgBIAEoBRIuCgV2YWx1ZRgCIAEoCzIfLk1haW5T",
            "ZXJ2ZXIuRW5jb3VudGVyTWV0aG9kRGF0YToCOAEi9AEKIVJlZ2lvbkFyZWFF",
            "bmNvdW50ZXJNZXRob2REYXRhTGlzdBJnChZlbmNvdW50ZXJfbWV0aG9kX2Rh",
            "dGFzGAEgAygLMkcuTWFpblNlcnZlci5SZWdpb25BcmVhRW5jb3VudGVyTWV0",
            "aG9kRGF0YUxpc3QuRW5jb3VudGVyTWV0aG9kRGF0YXNFbnRyeRpmChlFbmNv",
            "dW50ZXJNZXRob2REYXRhc0VudHJ5EgsKA2tleRgBIAEoCRI4CgV2YWx1ZRgC",
            "IAEoCzIpLk1haW5TZXJ2ZXIuUmVnaW9uQXJlYUVuY291bnRlck1ldGhvZERh",
            "dGE6AjgBIuUBChlSZWdpb25FbmNvdW50ZXJNZXRob2REYXRhEl0KFWVuY291",
            "bnRlcl9tZXRob2RfZGF0YRgBIAMoCzI+Lk1haW5TZXJ2ZXIuUmVnaW9uRW5j",
            "b3VudGVyTWV0aG9kRGF0YS5FbmNvdW50ZXJNZXRob2REYXRhRW50cnkaaQoY",
            "RW5jb3VudGVyTWV0aG9kRGF0YUVudHJ5EgsKA2tleRgBIAEoCRI8CgV2YWx1",
            "ZRgCIAEoCzItLk1haW5TZXJ2ZXIuUmVnaW9uQXJlYUVuY291bnRlck1ldGhv",
            "ZERhdGFMaXN0OgI4ASqWAQoMTWFpbkxhbmRUeXBlEhEKDU1haW5MYW5kX05v",
            "bmUQABIWChJNYWluTGFuZF9IZWFydEdvbGQQARIVChFNYWluTGFuZF9QbGF0",
            "aW51bRACEhIKDk1haW5MYW5kX1doaXRlEAMSFQoRTWFpbkxhbmRfSW5zdGFu",
            "Y2UQZBIZChVNYWluTGFuZF9NZXd0d29BbmRNZXcQZSppChBUcmFpbmVyTG9j",
            "U3RhdHVzEhIKDkxvY1N0YXR1c19Ob25lEAASFAoQTG9jU3RhdHVzX0JhdHRs",
            "ZRACEhUKEUxvY1N0YXR1c19GaXNoaW5nEAMSFAoQTG9jU3RhdHVzX1JlbW92",
            "ZRAHKvQBCgtMb2NBcmVhVHlwZRIUChBMb2NBcmVhVHlwZV9Ob25lEAASFQoR",
            "TG9jQXJlYVR5cGVfR3Jhc3MQARIWChJMb2NBcmVhVHlwZV9Gb3Jlc3QQAhIV",
            "ChFMb2NBcmVhVHlwZV9XYXRlchADEhQKEExvY0FyZWFUeXBlX1JvY2sQBBIU",
            "ChBMb2NBcmVhVHlwZV9Tbm93EAUSFgoSTG9jQXJlYVR5cGVfRGVzZXJ0EAYS",
            "FAoQTG9jQXJlYVR5cGVfQ2F2ZRAHEhgKFExvY0FyZWFUeXBlX0J1aWxkaW5n",
            "EAgSFQoRTG9jQXJlYVR5cGVfT3RoZXIQCSqoBAoPRW5jb3VudGVyTWV0aG9k",
            "EiAKHEVOQ09VTlRFUl9NRVRIT0RfVU5TUEVDSUZJRUQQABIICgRTVVJGEAES",
            "DQoJU1VQRVJfUk9EEAISCwoHT0xEX1JPRBADEgwKCEdPT0RfUk9EEAQSDAoI",
            "R0lGVF9FR0cQBRIICgRXQUxLEAYSDAoIT05MWV9PTkUQBxIICgRHSUZUEAgS",
            "DgoKUk9DS19TTUFTSBAJEg0KCVBPS0VGTFVURRAKEhEKDVNRVUlSVF9CT1RU",
            "TEUQCxITCg9TVVBFUl9ST0RfU1BPVFMQDBIOCgpTVVJGX1NQT1RTEA0SDgoK",
            "REFSS19HUkFTUxAOEg8KC0dSQVNTX1NQT1RTEA8SDgoKQ0FWRV9TUE9UUxAQ",
            "EhAKDEJSSURHRV9TUE9UUxAREg8KC0RFVk9OX1NDT1BFEBISEgoOWUVMTE9X",
            "X0ZMT1dFUlMQExIPCgtSRURfRkxPV0VSUxAUEhIKDlBVUlBMRV9GTE9XRVJT",
            "EBUSEQoNUk9VR0hfVEVSUkFJThAWEhEKDVNPU19FTkNPVU5URVIQFxIPCgtJ",
            "U0xBTkRfU0NBThAYEhIKDkJVQkJMSU5HX1NQT1RTEBkSDwoLQkVSUllfUElM",
            "RVMQGhINCglOUENfVFJBREUQGxIaChZTT1NfRlJPTV9CVUJCTElOR19TUE9U",
            "EBwSEQoNUk9BTUlOR19HUkFTUxAdEhEKDVJPQU1JTkdfV0FURVIQHkIhWh9n",
            "by1uYWthbWEtcG9rZS9wcm90by9NYWluU2VydmVyYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::MainServer.MainLandType), typeof(global::MainServer.TrainerLocStatus), typeof(global::MainServer.LocAreaType), typeof(global::MainServer.EncounterMethod), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.TrainerLoc), global::MainServer.TrainerLoc.Parser, new[]{ "X", "Y", "Z", "MainLandType", "PcName", "Status", "IsRun", "IsSurf", "TsMs", "Flag", "TrainerId", "FlagOther", "ServerLoopTs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.AOIBroadcast), global::MainServer.AOIBroadcast.Parser, new[]{ "MainLandType", "Players" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.EncounterMethodPokeDetail), global::MainServer.EncounterMethodPokeDetail.Parser, new[]{ "MinLevel", "MaxLevel", "MaxChance", "MethodString", "Method", "Version" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.EncounterMethodData), global::MainServer.EncounterMethodData.Parser, new[]{ "Pokes", "MaxChance" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.RegionAreaEncounterMethodData), global::MainServer.RegionAreaEncounterMethodData.Parser, new[]{ "EncounterMethodData" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.RegionAreaEncounterMethodDataList), global::MainServer.RegionAreaEncounterMethodDataList.Parser, new[]{ "EncounterMethodDatas" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::MainServer.RegionEncounterMethodData), global::MainServer.RegionEncounterMethodData.Parser, new[]{ "EncounterMethodData" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, })
          }));
    }
    #endregion

  }
  #region Enums
  public enum MainLandType {
    [pbr::OriginalName("MainLand_None")] MainLandNone = 0,
    [pbr::OriginalName("MainLand_HeartGold")] MainLandHeartGold = 1,
    [pbr::OriginalName("MainLand_Platinum")] MainLandPlatinum = 2,
    [pbr::OriginalName("MainLand_White")] MainLandWhite = 3,
    /// <summary>
    ///副本地图id锚点
    /// </summary>
    [pbr::OriginalName("MainLand_Instance")] MainLandInstance = 100,
    /// <summary>
    ///超梦与梦幻
    /// </summary>
    [pbr::OriginalName("MainLand_MewtwoAndMew")] MainLandMewtwoAndMew = 101,
  }

  public enum TrainerLocStatus {
    [pbr::OriginalName("LocStatus_None")] LocStatusNone = 0,
    /// <summary>
    /// LocStatus_Normal = 1;
    /// </summary>
    [pbr::OriginalName("LocStatus_Battle")] LocStatusBattle = 2,
    [pbr::OriginalName("LocStatus_Fishing")] LocStatusFishing = 3,
    /// <summary>
    /// LocStatus_Walk = 4;
    /// LocStatus_Run = 5;
    /// LocStatus_Surf = 6;//可以在水中战斗
    /// </summary>
    [pbr::OriginalName("LocStatus_Remove")] LocStatusRemove = 7,
  }

  public enum LocAreaType {
    [pbr::OriginalName("LocAreaType_None")] None = 0,
    [pbr::OriginalName("LocAreaType_Grass")] Grass = 1,
    [pbr::OriginalName("LocAreaType_Forest")] Forest = 2,
    [pbr::OriginalName("LocAreaType_Water")] Water = 3,
    [pbr::OriginalName("LocAreaType_Rock")] Rock = 4,
    [pbr::OriginalName("LocAreaType_Snow")] Snow = 5,
    [pbr::OriginalName("LocAreaType_Desert")] Desert = 6,
    [pbr::OriginalName("LocAreaType_Cave")] Cave = 7,
    [pbr::OriginalName("LocAreaType_Building")] Building = 8,
    [pbr::OriginalName("LocAreaType_Other")] Other = 9,
  }

  public enum EncounterMethod {
    /// <summary>
    /// 默认未知方式
    /// </summary>
    [pbr::OriginalName("ENCOUNTER_METHOD_UNSPECIFIED")] Unspecified = 0,
    /// <summary>
    /// 冲浪（在水面上）
    /// </summary>
    [pbr::OriginalName("SURF")] Surf = 1,
    /// <summary>
    /// 超级钓竿
    /// </summary>
    [pbr::OriginalName("SUPER_ROD")] SuperRod = 2,
    /// <summary>
    /// 老旧钓竿
    /// </summary>
    [pbr::OriginalName("OLD_ROD")] OldRod = 3,
    /// <summary>
    /// 好钓竿
    /// </summary>
    [pbr::OriginalName("GOOD_ROD")] GoodRod = 4,
    /// <summary>
    /// 赠送蛋
    /// </summary>
    [pbr::OriginalName("GIFT_EGG")] GiftEgg = 5,
    /// <summary>
    /// 普通走路遇敌（草地或洞穴中）
    /// </summary>
    [pbr::OriginalName("WALK")] Walk = 6,
    /// <summary>
    /// 仅限一次的特殊遭遇（如传说宝可梦）
    /// </summary>
    [pbr::OriginalName("ONLY_ONE")] OnlyOne = 7,
    /// <summary>
    /// NPC 直接赠送
    /// </summary>
    [pbr::OriginalName("GIFT")] Gift = 8,
    /// <summary>
    /// 使用碎石技能撞碎岩石
    /// </summary>
    [pbr::OriginalName("ROCK_SMASH")] RockSmash = 9,
    /// <summary>
    /// 使用宝可梦之笛触发的遭遇
    /// </summary>
    [pbr::OriginalName("POKEFLUTE")] Pokeflute = 10,
    /// <summary>
    /// 使用喷水壶唤醒树木状的宝可梦
    /// </summary>
    [pbr::OriginalName("SQUIRT_BOTTLE")] SquirtBottle = 11,
    /// <summary>
    /// 超级钓竿的特定钓鱼点
    /// </summary>
    [pbr::OriginalName("SUPER_ROD_SPOTS")] SuperRodSpots = 12,
    /// <summary>
    /// 冲浪的特定水域
    /// </summary>
    [pbr::OriginalName("SURF_SPOTS")] SurfSpots = 13,
    /// <summary>
    /// 黑草丛（可遇到双战/更强敌）
    /// </summary>
    [pbr::OriginalName("DARK_GRASS")] DarkGrass = 14,
    /// <summary>
    /// 草丛闪光点
    /// </summary>
    [pbr::OriginalName("GRASS_SPOTS")] GrassSpots = 15,
    /// <summary>
    /// 洞穴中罕见的遭遇点
    /// </summary>
    [pbr::OriginalName("CAVE_SPOTS")] CaveSpots = 16,
    /// <summary>
    /// 桥上的遭遇点（如天空之桥）
    /// </summary>
    [pbr::OriginalName("BRIDGE_SPOTS")] BridgeSpots = 17,
    /// <summary>
    /// 使用探测镜（Devon Scope）后可见的宝可梦
    /// </summary>
    [pbr::OriginalName("DEVON_SCOPE")] DevonScope = 18,
    /// <summary>
    /// 黄色花丛中的遭遇
    /// </summary>
    [pbr::OriginalName("YELLOW_FLOWERS")] YellowFlowers = 19,
    /// <summary>
    /// 红色花丛中的遭遇
    /// </summary>
    [pbr::OriginalName("RED_FLOWERS")] RedFlowers = 20,
    /// <summary>
    /// 紫色花丛中的遭遇
    /// </summary>
    [pbr::OriginalName("PURPLE_FLOWERS")] PurpleFlowers = 21,
    /// <summary>
    /// 粗糙地形（岩石区等）
    /// </summary>
    [pbr::OriginalName("ROUGH_TERRAIN")] RoughTerrain = 22,
    /// <summary>
    /// SOS 呼救战（宝可梦呼叫援军）
    /// </summary>
    [pbr::OriginalName("SOS_ENCOUNTER")] SosEncounter = 23,
    /// <summary>
    /// 岛屿扫描功能触发的遭遇
    /// </summary>
    [pbr::OriginalName("ISLAND_SCAN")] IslandScan = 24,
    /// <summary>
    /// 冒泡水域（特殊钓鱼点）
    /// </summary>
    [pbr::OriginalName("BUBBLING_SPOTS")] BubblingSpots = 25,
    /// <summary>
    /// 树果堆附近的遭遇
    /// </summary>
    [pbr::OriginalName("BERRY_PILES")] BerryPiles = 26,
    /// <summary>
    /// 与 NPC 交换获得
    /// </summary>
    [pbr::OriginalName("NPC_TRADE")] NpcTrade = 27,
    /// <summary>
    /// 从冒泡点中触发的 SOS 遭遇
    /// </summary>
    [pbr::OriginalName("SOS_FROM_BUBBLING_SPOT")] SosFromBubblingSpot = 28,
    /// <summary>
    /// 草地中游走宝可梦的遭遇
    /// </summary>
    [pbr::OriginalName("ROAMING_GRASS")] RoamingGrass = 29,
    /// <summary>
    /// 水面中游走宝可梦的遭遇
    /// </summary>
    [pbr::OriginalName("ROAMING_WATER")] RoamingWater = 30,
  }

  #endregion

  #region Messages
  /// <summary>
  /// message AoiUploadLoc {
  ///     float x = 1;
  ///     float y = 2;
  ///     float z = 3;
  ///     MainLandType main_land_type = 4;
  ///     // InstanceMapTpye instance_map_tpye = 5;
  ///     TrainerLocStatus status = 6;
  ///     int64 ts_ms = 7;
  /// }
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TrainerLoc : pb::IMessage<TrainerLoc>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TrainerLoc> _parser = new pb::MessageParser<TrainerLoc>(() => new TrainerLoc());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TrainerLoc> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TrainerLoc() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TrainerLoc(TrainerLoc other) : this() {
      x_ = other.x_;
      y_ = other.y_;
      z_ = other.z_;
      mainLandType_ = other.mainLandType_;
      pcName_ = other.pcName_;
      status_ = other.status_;
      isRun_ = other.isRun_;
      isSurf_ = other.isSurf_;
      tsMs_ = other.tsMs_;
      flag_ = other.flag_;
      trainerId_ = other.trainerId_;
      flagOther_ = other.flagOther_;
      serverLoopTs_ = other.serverLoopTs_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TrainerLoc Clone() {
      return new TrainerLoc(this);
    }

    /// <summary>Field number for the "x" field.</summary>
    public const int XFieldNumber = 1;
    private float x_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float X {
      get { return x_; }
      set {
        x_ = value;
      }
    }

    /// <summary>Field number for the "y" field.</summary>
    public const int YFieldNumber = 2;
    private float y_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float Y {
      get { return y_; }
      set {
        y_ = value;
      }
    }

    /// <summary>Field number for the "z" field.</summary>
    public const int ZFieldNumber = 3;
    private float z_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float Z {
      get { return z_; }
      set {
        z_ = value;
      }
    }

    /// <summary>Field number for the "main_land_type" field.</summary>
    public const int MainLandTypeFieldNumber = 4;
    private global::MainServer.MainLandType mainLandType_ = global::MainServer.MainLandType.MainLandNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.MainLandType MainLandType {
      get { return mainLandType_; }
      set {
        mainLandType_ = value;
      }
    }

    /// <summary>Field number for the "pc_name" field.</summary>
    public const int PcNameFieldNumber = 5;
    private string pcName_ = "";
    /// <summary>
    /// InstanceMapTpye instance_map_tpye = 5;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string PcName {
      get { return pcName_; }
      set {
        pcName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 6;
    private global::MainServer.TrainerLocStatus status_ = global::MainServer.TrainerLocStatus.LocStatusNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.TrainerLocStatus Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "is_run" field.</summary>
    public const int IsRunFieldNumber = 7;
    private bool isRun_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsRun {
      get { return isRun_; }
      set {
        isRun_ = value;
      }
    }

    /// <summary>Field number for the "is_surf" field.</summary>
    public const int IsSurfFieldNumber = 8;
    private bool isSurf_;
    /// <summary>
    ///在客户端中实时检测但是从水中出来或者进入水中需要这个
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsSurf {
      get { return isSurf_; }
      set {
        isSurf_ = value;
      }
    }

    /// <summary>Field number for the "ts_ms" field.</summary>
    public const int TsMsFieldNumber = 9;
    private long tsMs_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long TsMs {
      get { return tsMs_; }
      set {
        tsMs_ = value;
      }
    }

    /// <summary>Field number for the "flag" field.</summary>
    public const int FlagFieldNumber = 10;
    private uint flag_;
    /// <summary>
    ///客户端收到flag的结构不同，就需要去更新Trainer
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Flag {
      get { return flag_; }
      set {
        flag_ = value;
      }
    }

    /// <summary>Field number for the "trainer_id" field.</summary>
    public const int TrainerIdFieldNumber = 11;
    private long trainerId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long TrainerId {
      get { return trainerId_; }
      set {
        trainerId_ = value;
      }
    }

    /// <summary>Field number for the "flag_other" field.</summary>
    public const int FlagOtherFieldNumber = 12;
    private uint flagOther_;
    /// <summary>
    ///客户端收到flag_other的结构不同，暂不需要去更新Trainer,相差10更新一次
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint FlagOther {
      get { return flagOther_; }
      set {
        flagOther_ = value;
      }
    }

    /// <summary>Field number for the "server_loop_ts" field.</summary>
    public const int ServerLoopTsFieldNumber = 13;
    private uint serverLoopTs_;
    /// <summary>
    /// string regin_id = 1; //去除，战斗专用
    /// float x = 2;
    /// float y = 3;
    /// float z = 4;
    /// MainLandType main_land_type = 5;
    /// string map_name = 6; //去除
    /// LocAreaType area_type = 7; //去除
    /// float speed = 8;
    /// bool force_update = 9; // 是否强制更新 比如后续的跳跃等进行保留
    /// TrainerLocStatus status = 10;
    /// InstanceMapTpye instance_map_tpye = 11;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ServerLoopTs {
      get { return serverLoopTs_; }
      set {
        serverLoopTs_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TrainerLoc);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TrainerLoc other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(X, other.X)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Y, other.Y)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Z, other.Z)) return false;
      if (MainLandType != other.MainLandType) return false;
      if (PcName != other.PcName) return false;
      if (Status != other.Status) return false;
      if (IsRun != other.IsRun) return false;
      if (IsSurf != other.IsSurf) return false;
      if (TsMs != other.TsMs) return false;
      if (Flag != other.Flag) return false;
      if (TrainerId != other.TrainerId) return false;
      if (FlagOther != other.FlagOther) return false;
      if (ServerLoopTs != other.ServerLoopTs) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (X != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(X);
      if (Y != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Y);
      if (Z != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Z);
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) hash ^= MainLandType.GetHashCode();
      if (PcName.Length != 0) hash ^= PcName.GetHashCode();
      if (Status != global::MainServer.TrainerLocStatus.LocStatusNone) hash ^= Status.GetHashCode();
      if (IsRun != false) hash ^= IsRun.GetHashCode();
      if (IsSurf != false) hash ^= IsSurf.GetHashCode();
      if (TsMs != 0L) hash ^= TsMs.GetHashCode();
      if (Flag != 0) hash ^= Flag.GetHashCode();
      if (TrainerId != 0L) hash ^= TrainerId.GetHashCode();
      if (FlagOther != 0) hash ^= FlagOther.GetHashCode();
      if (ServerLoopTs != 0) hash ^= ServerLoopTs.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (X != 0F) {
        output.WriteRawTag(13);
        output.WriteFloat(X);
      }
      if (Y != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(Y);
      }
      if (Z != 0F) {
        output.WriteRawTag(29);
        output.WriteFloat(Z);
      }
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) MainLandType);
      }
      if (PcName.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(PcName);
      }
      if (Status != global::MainServer.TrainerLocStatus.LocStatusNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) Status);
      }
      if (IsRun != false) {
        output.WriteRawTag(56);
        output.WriteBool(IsRun);
      }
      if (IsSurf != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsSurf);
      }
      if (TsMs != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(TsMs);
      }
      if (Flag != 0) {
        output.WriteRawTag(80);
        output.WriteUInt32(Flag);
      }
      if (TrainerId != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(TrainerId);
      }
      if (FlagOther != 0) {
        output.WriteRawTag(96);
        output.WriteUInt32(FlagOther);
      }
      if (ServerLoopTs != 0) {
        output.WriteRawTag(104);
        output.WriteUInt32(ServerLoopTs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (X != 0F) {
        output.WriteRawTag(13);
        output.WriteFloat(X);
      }
      if (Y != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(Y);
      }
      if (Z != 0F) {
        output.WriteRawTag(29);
        output.WriteFloat(Z);
      }
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) MainLandType);
      }
      if (PcName.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(PcName);
      }
      if (Status != global::MainServer.TrainerLocStatus.LocStatusNone) {
        output.WriteRawTag(48);
        output.WriteEnum((int) Status);
      }
      if (IsRun != false) {
        output.WriteRawTag(56);
        output.WriteBool(IsRun);
      }
      if (IsSurf != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsSurf);
      }
      if (TsMs != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(TsMs);
      }
      if (Flag != 0) {
        output.WriteRawTag(80);
        output.WriteUInt32(Flag);
      }
      if (TrainerId != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(TrainerId);
      }
      if (FlagOther != 0) {
        output.WriteRawTag(96);
        output.WriteUInt32(FlagOther);
      }
      if (ServerLoopTs != 0) {
        output.WriteRawTag(104);
        output.WriteUInt32(ServerLoopTs);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (X != 0F) {
        size += 1 + 4;
      }
      if (Y != 0F) {
        size += 1 + 4;
      }
      if (Z != 0F) {
        size += 1 + 4;
      }
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) MainLandType);
      }
      if (PcName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(PcName);
      }
      if (Status != global::MainServer.TrainerLocStatus.LocStatusNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Status);
      }
      if (IsRun != false) {
        size += 1 + 1;
      }
      if (IsSurf != false) {
        size += 1 + 1;
      }
      if (TsMs != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(TsMs);
      }
      if (Flag != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Flag);
      }
      if (TrainerId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(TrainerId);
      }
      if (FlagOther != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(FlagOther);
      }
      if (ServerLoopTs != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ServerLoopTs);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TrainerLoc other) {
      if (other == null) {
        return;
      }
      if (other.X != 0F) {
        X = other.X;
      }
      if (other.Y != 0F) {
        Y = other.Y;
      }
      if (other.Z != 0F) {
        Z = other.Z;
      }
      if (other.MainLandType != global::MainServer.MainLandType.MainLandNone) {
        MainLandType = other.MainLandType;
      }
      if (other.PcName.Length != 0) {
        PcName = other.PcName;
      }
      if (other.Status != global::MainServer.TrainerLocStatus.LocStatusNone) {
        Status = other.Status;
      }
      if (other.IsRun != false) {
        IsRun = other.IsRun;
      }
      if (other.IsSurf != false) {
        IsSurf = other.IsSurf;
      }
      if (other.TsMs != 0L) {
        TsMs = other.TsMs;
      }
      if (other.Flag != 0) {
        Flag = other.Flag;
      }
      if (other.TrainerId != 0L) {
        TrainerId = other.TrainerId;
      }
      if (other.FlagOther != 0) {
        FlagOther = other.FlagOther;
      }
      if (other.ServerLoopTs != 0) {
        ServerLoopTs = other.ServerLoopTs;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 13: {
            X = input.ReadFloat();
            break;
          }
          case 21: {
            Y = input.ReadFloat();
            break;
          }
          case 29: {
            Z = input.ReadFloat();
            break;
          }
          case 32: {
            MainLandType = (global::MainServer.MainLandType) input.ReadEnum();
            break;
          }
          case 42: {
            PcName = input.ReadString();
            break;
          }
          case 48: {
            Status = (global::MainServer.TrainerLocStatus) input.ReadEnum();
            break;
          }
          case 56: {
            IsRun = input.ReadBool();
            break;
          }
          case 64: {
            IsSurf = input.ReadBool();
            break;
          }
          case 72: {
            TsMs = input.ReadInt64();
            break;
          }
          case 80: {
            Flag = input.ReadUInt32();
            break;
          }
          case 88: {
            TrainerId = input.ReadInt64();
            break;
          }
          case 96: {
            FlagOther = input.ReadUInt32();
            break;
          }
          case 104: {
            ServerLoopTs = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 13: {
            X = input.ReadFloat();
            break;
          }
          case 21: {
            Y = input.ReadFloat();
            break;
          }
          case 29: {
            Z = input.ReadFloat();
            break;
          }
          case 32: {
            MainLandType = (global::MainServer.MainLandType) input.ReadEnum();
            break;
          }
          case 42: {
            PcName = input.ReadString();
            break;
          }
          case 48: {
            Status = (global::MainServer.TrainerLocStatus) input.ReadEnum();
            break;
          }
          case 56: {
            IsRun = input.ReadBool();
            break;
          }
          case 64: {
            IsSurf = input.ReadBool();
            break;
          }
          case 72: {
            TsMs = input.ReadInt64();
            break;
          }
          case 80: {
            Flag = input.ReadUInt32();
            break;
          }
          case 88: {
            TrainerId = input.ReadInt64();
            break;
          }
          case 96: {
            FlagOther = input.ReadUInt32();
            break;
          }
          case 104: {
            ServerLoopTs = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AOIBroadcast : pb::IMessage<AOIBroadcast>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AOIBroadcast> _parser = new pb::MessageParser<AOIBroadcast>(() => new AOIBroadcast());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AOIBroadcast> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AOIBroadcast() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AOIBroadcast(AOIBroadcast other) : this() {
      mainLandType_ = other.mainLandType_;
      players_ = other.players_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AOIBroadcast Clone() {
      return new AOIBroadcast(this);
    }

    /// <summary>Field number for the "main_land_type" field.</summary>
    public const int MainLandTypeFieldNumber = 1;
    private global::MainServer.MainLandType mainLandType_ = global::MainServer.MainLandType.MainLandNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.MainLandType MainLandType {
      get { return mainLandType_; }
      set {
        mainLandType_ = value;
      }
    }

    /// <summary>Field number for the "players" field.</summary>
    public const int PlayersFieldNumber = 3;
    private static readonly pb::FieldCodec<global::MainServer.TrainerLoc> _repeated_players_codec
        = pb::FieldCodec.ForMessage(26, global::MainServer.TrainerLoc.Parser);
    private readonly pbc::RepeatedField<global::MainServer.TrainerLoc> players_ = new pbc::RepeatedField<global::MainServer.TrainerLoc>();
    /// <summary>
    /// InstanceMapTpye instance_map_tpye = 2;
    /// uint64 server_tick = 2;
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::MainServer.TrainerLoc> Players {
      get { return players_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AOIBroadcast);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AOIBroadcast other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MainLandType != other.MainLandType) return false;
      if(!players_.Equals(other.players_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) hash ^= MainLandType.GetHashCode();
      hash ^= players_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) MainLandType);
      }
      players_.WriteTo(output, _repeated_players_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) MainLandType);
      }
      players_.WriteTo(ref output, _repeated_players_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MainLandType != global::MainServer.MainLandType.MainLandNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) MainLandType);
      }
      size += players_.CalculateSize(_repeated_players_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AOIBroadcast other) {
      if (other == null) {
        return;
      }
      if (other.MainLandType != global::MainServer.MainLandType.MainLandNone) {
        MainLandType = other.MainLandType;
      }
      players_.Add(other.players_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MainLandType = (global::MainServer.MainLandType) input.ReadEnum();
            break;
          }
          case 26: {
            players_.AddEntriesFrom(input, _repeated_players_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MainLandType = (global::MainServer.MainLandType) input.ReadEnum();
            break;
          }
          case 26: {
            players_.AddEntriesFrom(ref input, _repeated_players_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class EncounterMethodPokeDetail : pb::IMessage<EncounterMethodPokeDetail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EncounterMethodPokeDetail> _parser = new pb::MessageParser<EncounterMethodPokeDetail>(() => new EncounterMethodPokeDetail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<EncounterMethodPokeDetail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EncounterMethodPokeDetail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EncounterMethodPokeDetail(EncounterMethodPokeDetail other) : this() {
      minLevel_ = other.minLevel_;
      maxLevel_ = other.maxLevel_;
      maxChance_ = other.maxChance_;
      methodString_ = other.methodString_;
      method_ = other.method_;
      version_ = other.version_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EncounterMethodPokeDetail Clone() {
      return new EncounterMethodPokeDetail(this);
    }

    /// <summary>Field number for the "min_level" field.</summary>
    public const int MinLevelFieldNumber = 1;
    private int minLevel_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MinLevel {
      get { return minLevel_; }
      set {
        minLevel_ = value;
      }
    }

    /// <summary>Field number for the "max_level" field.</summary>
    public const int MaxLevelFieldNumber = 2;
    private int maxLevel_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MaxLevel {
      get { return maxLevel_; }
      set {
        maxLevel_ = value;
      }
    }

    /// <summary>Field number for the "max_chance" field.</summary>
    public const int MaxChanceFieldNumber = 3;
    private int maxChance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MaxChance {
      get { return maxChance_; }
      set {
        maxChance_ = value;
      }
    }

    /// <summary>Field number for the "method_string" field.</summary>
    public const int MethodStringFieldNumber = 4;
    private string methodString_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string MethodString {
      get { return methodString_; }
      set {
        methodString_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "method" field.</summary>
    public const int MethodFieldNumber = 5;
    private global::MainServer.EncounterMethod method_ = global::MainServer.EncounterMethod.Unspecified;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::MainServer.EncounterMethod Method {
      get { return method_; }
      set {
        method_ = value;
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 6;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as EncounterMethodPokeDetail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(EncounterMethodPokeDetail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MinLevel != other.MinLevel) return false;
      if (MaxLevel != other.MaxLevel) return false;
      if (MaxChance != other.MaxChance) return false;
      if (MethodString != other.MethodString) return false;
      if (Method != other.Method) return false;
      if (Version != other.Version) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MinLevel != 0) hash ^= MinLevel.GetHashCode();
      if (MaxLevel != 0) hash ^= MaxLevel.GetHashCode();
      if (MaxChance != 0) hash ^= MaxChance.GetHashCode();
      if (MethodString.Length != 0) hash ^= MethodString.GetHashCode();
      if (Method != global::MainServer.EncounterMethod.Unspecified) hash ^= Method.GetHashCode();
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MinLevel != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MinLevel);
      }
      if (MaxLevel != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MaxLevel);
      }
      if (MaxChance != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MaxChance);
      }
      if (MethodString.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(MethodString);
      }
      if (Method != global::MainServer.EncounterMethod.Unspecified) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Method);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MinLevel != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(MinLevel);
      }
      if (MaxLevel != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MaxLevel);
      }
      if (MaxChance != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MaxChance);
      }
      if (MethodString.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(MethodString);
      }
      if (Method != global::MainServer.EncounterMethod.Unspecified) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Method);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MinLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MinLevel);
      }
      if (MaxLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MaxLevel);
      }
      if (MaxChance != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MaxChance);
      }
      if (MethodString.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(MethodString);
      }
      if (Method != global::MainServer.EncounterMethod.Unspecified) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Method);
      }
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(EncounterMethodPokeDetail other) {
      if (other == null) {
        return;
      }
      if (other.MinLevel != 0) {
        MinLevel = other.MinLevel;
      }
      if (other.MaxLevel != 0) {
        MaxLevel = other.MaxLevel;
      }
      if (other.MaxChance != 0) {
        MaxChance = other.MaxChance;
      }
      if (other.MethodString.Length != 0) {
        MethodString = other.MethodString;
      }
      if (other.Method != global::MainServer.EncounterMethod.Unspecified) {
        Method = other.Method;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MinLevel = input.ReadInt32();
            break;
          }
          case 16: {
            MaxLevel = input.ReadInt32();
            break;
          }
          case 24: {
            MaxChance = input.ReadInt32();
            break;
          }
          case 34: {
            MethodString = input.ReadString();
            break;
          }
          case 40: {
            Method = (global::MainServer.EncounterMethod) input.ReadEnum();
            break;
          }
          case 50: {
            Version = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MinLevel = input.ReadInt32();
            break;
          }
          case 16: {
            MaxLevel = input.ReadInt32();
            break;
          }
          case 24: {
            MaxChance = input.ReadInt32();
            break;
          }
          case 34: {
            MethodString = input.ReadString();
            break;
          }
          case 40: {
            Method = (global::MainServer.EncounterMethod) input.ReadEnum();
            break;
          }
          case 50: {
            Version = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class EncounterMethodData : pb::IMessage<EncounterMethodData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<EncounterMethodData> _parser = new pb::MessageParser<EncounterMethodData>(() => new EncounterMethodData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<EncounterMethodData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EncounterMethodData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EncounterMethodData(EncounterMethodData other) : this() {
      pokes_ = other.pokes_.Clone();
      maxChance_ = other.maxChance_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public EncounterMethodData Clone() {
      return new EncounterMethodData(this);
    }

    /// <summary>Field number for the "pokes" field.</summary>
    public const int PokesFieldNumber = 1;
    private static readonly pbc::MapField<string, global::MainServer.EncounterMethodPokeDetail>.Codec _map_pokes_codec
        = new pbc::MapField<string, global::MainServer.EncounterMethodPokeDetail>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::MainServer.EncounterMethodPokeDetail.Parser), 10);
    private readonly pbc::MapField<string, global::MainServer.EncounterMethodPokeDetail> pokes_ = new pbc::MapField<string, global::MainServer.EncounterMethodPokeDetail>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::MainServer.EncounterMethodPokeDetail> Pokes {
      get { return pokes_; }
    }

    /// <summary>Field number for the "max_chance" field.</summary>
    public const int MaxChanceFieldNumber = 2;
    private int maxChance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MaxChance {
      get { return maxChance_; }
      set {
        maxChance_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as EncounterMethodData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(EncounterMethodData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!Pokes.Equals(other.Pokes)) return false;
      if (MaxChance != other.MaxChance) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= Pokes.GetHashCode();
      if (MaxChance != 0) hash ^= MaxChance.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      pokes_.WriteTo(output, _map_pokes_codec);
      if (MaxChance != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MaxChance);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      pokes_.WriteTo(ref output, _map_pokes_codec);
      if (MaxChance != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(MaxChance);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += pokes_.CalculateSize(_map_pokes_codec);
      if (MaxChance != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MaxChance);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(EncounterMethodData other) {
      if (other == null) {
        return;
      }
      pokes_.MergeFrom(other.pokes_);
      if (other.MaxChance != 0) {
        MaxChance = other.MaxChance;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            pokes_.AddEntriesFrom(input, _map_pokes_codec);
            break;
          }
          case 16: {
            MaxChance = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            pokes_.AddEntriesFrom(ref input, _map_pokes_codec);
            break;
          }
          case 16: {
            MaxChance = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RegionAreaEncounterMethodData : pb::IMessage<RegionAreaEncounterMethodData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RegionAreaEncounterMethodData> _parser = new pb::MessageParser<RegionAreaEncounterMethodData>(() => new RegionAreaEncounterMethodData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RegionAreaEncounterMethodData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionAreaEncounterMethodData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionAreaEncounterMethodData(RegionAreaEncounterMethodData other) : this() {
      encounterMethodData_ = other.encounterMethodData_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionAreaEncounterMethodData Clone() {
      return new RegionAreaEncounterMethodData(this);
    }

    /// <summary>Field number for the "encounter_method_data" field.</summary>
    public const int EncounterMethodDataFieldNumber = 1;
    private static readonly pbc::MapField<int, global::MainServer.EncounterMethodData>.Codec _map_encounterMethodData_codec
        = new pbc::MapField<int, global::MainServer.EncounterMethodData>.Codec(pb::FieldCodec.ForInt32(8, 0), pb::FieldCodec.ForMessage(18, global::MainServer.EncounterMethodData.Parser), 10);
    private readonly pbc::MapField<int, global::MainServer.EncounterMethodData> encounterMethodData_ = new pbc::MapField<int, global::MainServer.EncounterMethodData>();
    /// <summary>
    ///EncounterMethod -> EncounterMethodData
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<int, global::MainServer.EncounterMethodData> EncounterMethodData {
      get { return encounterMethodData_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RegionAreaEncounterMethodData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RegionAreaEncounterMethodData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!EncounterMethodData.Equals(other.EncounterMethodData)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= EncounterMethodData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      encounterMethodData_.WriteTo(output, _map_encounterMethodData_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      encounterMethodData_.WriteTo(ref output, _map_encounterMethodData_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += encounterMethodData_.CalculateSize(_map_encounterMethodData_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RegionAreaEncounterMethodData other) {
      if (other == null) {
        return;
      }
      encounterMethodData_.MergeFrom(other.encounterMethodData_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            encounterMethodData_.AddEntriesFrom(input, _map_encounterMethodData_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            encounterMethodData_.AddEntriesFrom(ref input, _map_encounterMethodData_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RegionAreaEncounterMethodDataList : pb::IMessage<RegionAreaEncounterMethodDataList>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RegionAreaEncounterMethodDataList> _parser = new pb::MessageParser<RegionAreaEncounterMethodDataList>(() => new RegionAreaEncounterMethodDataList());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RegionAreaEncounterMethodDataList> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionAreaEncounterMethodDataList() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionAreaEncounterMethodDataList(RegionAreaEncounterMethodDataList other) : this() {
      encounterMethodDatas_ = other.encounterMethodDatas_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionAreaEncounterMethodDataList Clone() {
      return new RegionAreaEncounterMethodDataList(this);
    }

    /// <summary>Field number for the "encounter_method_datas" field.</summary>
    public const int EncounterMethodDatasFieldNumber = 1;
    private static readonly pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodData>.Codec _map_encounterMethodDatas_codec
        = new pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodData>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::MainServer.RegionAreaEncounterMethodData.Parser), 10);
    private readonly pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodData> encounterMethodDatas_ = new pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodData>();
    /// <summary>
    ///region area ->
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodData> EncounterMethodDatas {
      get { return encounterMethodDatas_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RegionAreaEncounterMethodDataList);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RegionAreaEncounterMethodDataList other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!EncounterMethodDatas.Equals(other.EncounterMethodDatas)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= EncounterMethodDatas.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      encounterMethodDatas_.WriteTo(output, _map_encounterMethodDatas_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      encounterMethodDatas_.WriteTo(ref output, _map_encounterMethodDatas_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += encounterMethodDatas_.CalculateSize(_map_encounterMethodDatas_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RegionAreaEncounterMethodDataList other) {
      if (other == null) {
        return;
      }
      encounterMethodDatas_.MergeFrom(other.encounterMethodDatas_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            encounterMethodDatas_.AddEntriesFrom(input, _map_encounterMethodDatas_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            encounterMethodDatas_.AddEntriesFrom(ref input, _map_encounterMethodDatas_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RegionEncounterMethodData : pb::IMessage<RegionEncounterMethodData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RegionEncounterMethodData> _parser = new pb::MessageParser<RegionEncounterMethodData>(() => new RegionEncounterMethodData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RegionEncounterMethodData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::MainServer.LocInfoReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionEncounterMethodData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionEncounterMethodData(RegionEncounterMethodData other) : this() {
      encounterMethodData_ = other.encounterMethodData_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegionEncounterMethodData Clone() {
      return new RegionEncounterMethodData(this);
    }

    /// <summary>Field number for the "encounter_method_data" field.</summary>
    public const int EncounterMethodDataFieldNumber = 1;
    private static readonly pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodDataList>.Codec _map_encounterMethodData_codec
        = new pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodDataList>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::MainServer.RegionAreaEncounterMethodDataList.Parser), 10);
    private readonly pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodDataList> encounterMethodData_ = new pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodDataList>();
    /// <summary>
    ///region ->
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::MainServer.RegionAreaEncounterMethodDataList> EncounterMethodData {
      get { return encounterMethodData_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RegionEncounterMethodData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RegionEncounterMethodData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!EncounterMethodData.Equals(other.EncounterMethodData)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= EncounterMethodData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      encounterMethodData_.WriteTo(output, _map_encounterMethodData_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      encounterMethodData_.WriteTo(ref output, _map_encounterMethodData_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += encounterMethodData_.CalculateSize(_map_encounterMethodData_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RegionEncounterMethodData other) {
      if (other == null) {
        return;
      }
      encounterMethodData_.MergeFrom(other.encounterMethodData_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            encounterMethodData_.AddEntriesFrom(input, _map_encounterMethodData_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            encounterMethodData_.AddEntriesFrom(ref input, _map_encounterMethodData_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
